import React from 'react';
import Image from 'next/image';
import { DiscordMessage } from '../src/types/discord';
import styles from '../styles/DiscordMessage.module.css';

interface DiscordMessageProps {
  message: DiscordMessage;
}

const DiscordMessageComponent: React.FC<DiscordMessageProps> = ({ message }) => {
  // Function to render message content with colored role mentions
  const renderContent = () => {
    if (!message.content || message.content.trim() === '') return null;
    
    // If there are no role mentions, just return the content
    if (!message.roleMentions || message.roleMentions.length === 0) {
      return <p className={styles.messageContent}>{message.content}</p>;
    }
    
    // Split the content by words to identify and style role mentions
    const words = message.content.split(' ');
    
    return (
      <p className={styles.messageContent}>
        {words.map((word, index) => {
          // Check if this word is a role mention (starts with @)
          if (word.startsWith('@')) {
            // Find the matching role data
            // Add null check with optional chaining
            const roleMention = message.roleMentions?.find(role => 
              word === `@${role.name}`
            );
            
            if (roleMention) {
              // Use Discord's standard blue color for all role mentions
              return (
                <span 
                  key={index}
                  className={styles.roleMention}
                >
                  {word}{' '}
                </span>
              );
            }
          }
          
          // Regular word
          return <span key={index}>{word} </span>;
        })}
      </p>
    );
  };

  return (
    <div className={styles.discordMessage}>
      {renderContent()}
      
      {message.imageUrl && (
        <div className={styles.imageContainer}>
          <Image
            src={message.imageUrl}
            alt="Discord attachment"
            width={500}
            height={300}
            layout="responsive"
            className={styles.messageImage}
          />
        </div>
      )}
    </div>
  );
};

export default DiscordMessageComponent;






