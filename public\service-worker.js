// Service worker for push notifications
self.addEventListener('install', (event) => {
  console.log('Service Worker installed');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('Service Worker activated');
  return self.clients.claim();
});

self.addEventListener('push', function(event) {
  console.log('[Service Worker] Push received');
  
  if (!event.data) {
    console.log('[Service Worker] No data received with push event');
    return;
  }
  
  try {
    const data = event.data.json();
    console.log('[Service Worker] Push data:', data);
    
    const title = data.title || 'New Discord Message';
    const options = {
      body: data.content || 'New Discord message',
      icon: '/favicon.ico', // Fallback to a simple icon that definitely exists
      badge: '/favicon.ico',
      image: data.imageUrl || null,
      data: {
        url: data.url || '/discord'
      },
      vibrate: [100, 50, 100],
      timestamp: Date.now(),
      actions: [
        {
          action: 'open',
          title: 'View Message'
        }
      ]
    };
    
    console.log('[Service Worker] Showing notification with title:', title);
    console.log('[Service Worker] Notification options:', JSON.stringify(options));
    
    event.waitUntil(
      self.registration.showNotification(title, options)
        .then(() => {
          console.log('[Service Worker] Notification shown successfully');
        })
        .catch(error => {
          console.error('[Service Worker] Error showing notification:', error);
        })
    );
  } catch (error) {
    console.error('[Service Worker] Error processing push data:', error);
    // Fallback notification in case of parsing error
    event.waitUntil(
      self.registration.showNotification(
        'New Discord Message',
        {
          body: 'Check out the latest updates in Discord',
          icon: '/favicon.ico',
          badge: '/favicon.ico',
          data: {
            url: '/discord'
          }
        }
      )
    );
  }
});

self.addEventListener('notificationclick', function(event) {
  console.log('[Service Worker] Notification clicked:', event.notification.title);
  
  event.notification.close();
  
  const urlToOpen = event.notification.data?.url || '/discord';
  console.log('[Service Worker] Opening URL:', urlToOpen);
  
  event.waitUntil(
    clients.matchAll({ type: 'window' }).then(function(clientList) {
      // Check if there's already a window open
      for (const client of clientList) {
        if (client.url.includes(urlToOpen) && 'focus' in client) {
          return client.focus();
        }
      }
      // If no window is open, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
});

// Add this to ensure the service worker stays active
self.addEventListener('fetch', function(event) {
  // This empty fetch handler is needed to keep the service worker active
  // for some browsers
});

