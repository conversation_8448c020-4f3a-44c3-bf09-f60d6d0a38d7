// Header.tsx
import { <PERSON>, <PERSON><PERSON>, Container, Card, styled, <PERSON>lt<PERSON>, IconButton } from '@mui/material';
import Link from 'src/components/Link';
import Logo from 'src/components/LogoSign';
import { signIn, signOut, useSession } from "next-auth/react";
import SettingsIcon from '@mui/icons-material/Settings';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';

const HeaderWrapper = styled(Card)(
  ({ theme }) => `
    width: 100%;
    display: flex;
    align-items: center;
    height: ${theme.spacing(10)};
    margin-bottom: ${theme.spacing(10)};
  `
);

function Header() {
  const { data: session } = useSession();

  return (
    <HeaderWrapper>
      <Container maxWidth="lg">
        <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
          <Box display="flex" alignItems="center" gap={2}>
            <Logo />
            <Button component={Link} href="/" variant="text" sx={{ color: 'text.primary', fontSize: '1rem', textTransform: 'none' }}>Home</Button>
            <Button component={Link} href="/discord" variant="text" sx={{ color: 'text.primary', fontSize: '1rem', textTransform: 'none' }}>Discord</Button>
            <Button component={Link} href="/lobbies" variant="text" sx={{ color: 'text.primary', fontSize: '1rem', textTransform: 'none' }}>Game Lobbies</Button>
            {/*<Button component={Link} href="/about-us" variant="text" sx={{ color: 'text.primary', fontSize: '1rem', textTransform: 'none' }}>About Us</Button>
            <Button component={Link} href="/contact" variant="text" sx={{ color: 'text.primary', fontSize: '1rem', textTransform: 'none' }}>Contact Me</Button>*/}
          </Box>
          {session ? (
            <>
              <Tooltip title="Notifications">
                <IconButton
                  color="inherit"
                  component={Link}
                  href="/notifications"
                  sx={{ ml: 1 }}
                >
                  <NotificationsActiveIcon />
                </IconButton>
              </Tooltip>
              {/*<Tooltip title="Settings">
                <IconButton
                  color="inherit"
                  component={Link}
                  href="/settings"
                  sx={{ ml: 1 }}
                >
                  <SettingsIcon />
                </IconButton>
              </Tooltip>*/}
              <Button
                variant="contained"
                sx={{ ml: 2 }}
                onClick={() => signOut()}
              >
                Logout ({session.user?.name || "Discord User"})
              </Button>
            </>
          ) : (
            <Button
              variant="contained"
              sx={{ ml: 2 }}
              onClick={() => signIn("discord")}
            >
              Login with Discord
            </Button>
          )}
        </Box>
      </Container>
    </HeaderWrapper>
  );
}

export default Header;
