import type { NextApiRequest, NextApiResponse } from 'next/types';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Check for authorization
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ') || authHeader.split(' ')[1] !== process.env.API_SECRET_KEY) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Return environment variable status (not the actual values for security)
  return res.status(200).json({
    environment: process.env.NODE_ENV || 'not set',
    variables: {
      API_SECRET_KEY: process.env.API_SECRET_KEY ? 'set' : 'not set',
      NEXT_PUBLIC_VAPID_KEY: process.env.NEXT_PUBLIC_VAPID_KEY ? 'set' : 'not set',
      VAPID_PRIVATE_KEY: process.env.VAPID_PRIVATE_KEY ? 'set' : 'not set',
      MONGODB_URI: process.env.MONGODB_URI ? 'set' : 'not set',
      VERCEL_URL: process.env.VERCEL_URL ? process.env.VERCEL_URL : 'not set',
      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL ? process.env.NEXT_PUBLIC_API_URL : 'not set'
    }
  });
}