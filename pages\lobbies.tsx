import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useSession } from 'next-auth/react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Fab,
  Dialog,
  DialogContent
} from '@mui/material';
import { Add, Refresh } from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { GameLobby } from '../src/types/lobby';
import { useLobby } from '../src/hooks/useLobby';
import LobbyCard from '../src/components/lobby/LobbyCard';
import CreateLobbyForm from '../src/components/lobby/CreateLobbyForm';

const StyledContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(4),
}));

const StyledFab = styled(Fab)(({ theme }) => ({
  position: 'fixed',
  bottom: theme.spacing(2),
  right: theme.spacing(2),
}));

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`lobbies-tabpanel-${index}`}
      aria-labelledby={`lobbies-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

export default function LobbiesPage() {
  const { data: session, status } = useSession();
  const { getLobbies, getOrganizerConfig, loading, error } = useLobby();
  
  const [lobbies, setLobbies] = useState<GameLobby[]>([]);
  const [isOrganizer, setIsOrganizer] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const currentUserId = (session?.user as any)?.discordId || (session?.user as any)?.id || session?.user?.email?.split('@')[0] || 'unknown';

  const loadLobbies = async () => {
    try {
      setRefreshing(true);
      const allLobbies = await getLobbies();
      setLobbies(allLobbies);
    } catch (err) {
      console.error('Failed to load lobbies:', err);
    } finally {
      setRefreshing(false);
    }
  };

  const checkOrganizerStatus = async () => {
    try {
      const config = await getOrganizerConfig();
      setIsOrganizer(config.discordIds.includes(currentUserId));
    } catch (err) {
      console.error('Failed to check organizer status:', err);
      setIsOrganizer(false);
    }
  };

  useEffect(() => {
    if (status === 'authenticated') {
      loadLobbies();
      checkOrganizerStatus();
    }
  }, [status]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleLobbyCreated = (lobby: GameLobby) => {
    setLobbies(prev => [lobby, ...prev]);
    setCreateDialogOpen(false);
  };

  const handleLobbyDeleted = (lobbyId: string) => {
    setLobbies(prev => prev.filter(lobby => lobby.id !== lobbyId));
  };

  const handleSignupChanged = () => {
    loadLobbies(); // Refresh lobbies when signup changes
  };

  const getFilteredLobbies = () => {
    switch (tabValue) {
      case 0: // All lobbies
        return lobbies.filter(lobby => lobby.status === 'open' || lobby.status === 'full');
      case 1: // My lobbies (as organizer)
        return lobbies.filter(lobby => lobby.organizerId === currentUserId);
      case 2: // Joined lobbies
        return lobbies.filter(lobby =>
          lobby.slots.some(slot => slot.user?.id === currentUserId && slot.type === 'passenger')
        );
      default:
        return lobbies;
    }
  };

  if (status === 'loading') {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
        <CircularProgress />
      </Box>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <StyledContainer maxWidth="md">
        <Alert severity="warning">
          Please sign in with Discord to view and join game lobbies.
        </Alert>
      </StyledContainer>
    );
  }

  return (
    <>
      <Head>
        <title>Carry Runs - Loapals</title>
        <meta name="description" content="Join carry runs and get carried through challenging content by experienced players" />
      </Head>

      <StyledContainer maxWidth="lg">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1">
            Carry Runs
          </Typography>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadLobbies}
            disabled={loading || refreshing}
          >
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </Box>

        <Typography variant="h6" color="text.secondary" paragraph>
          Join carry runs organized by experienced players. Sign up for passenger slots and get carried through challenging content!
        </Typography>

        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Available Carries" />
            <Tab label="My Carries" />
            <Tab label="Joined Carries" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          {loading && lobbies.length === 0 ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : getFilteredLobbies().length === 0 ? (
            <Card>
              <CardContent>
                <Typography variant="h6" align="center" color="text.secondary">
                  No carry runs available at the moment
                </Typography>
                <Typography variant="body2" align="center" color="text.secondary">
                  Check back later or ask an organizer to create a new carry run.
                </Typography>
              </CardContent>
            </Card>
          ) : (
            <Grid container spacing={3}>
              {getFilteredLobbies().map((lobby) => (
                <Grid item xs={12} key={lobby.id}>
                  <LobbyCard
                    lobby={lobby}
                    onLobbyDeleted={handleLobbyDeleted}
                    onSignupChanged={handleSignupChanged}
                  />
                </Grid>
              ))}
            </Grid>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {!isOrganizer ? (
            <Card>
              <CardContent>
                <Typography variant="h6" align="center" color="text.secondary">
                  You are not authorized to create lobbies
                </Typography>
                <Typography variant="body2" align="center" color="text.secondary">
                  Contact an administrator to become an organizer.
                </Typography>
              </CardContent>
            </Card>
          ) : getFilteredLobbies().length === 0 ? (
            <Card>
              <CardContent>
                <Typography variant="h6" align="center" color="text.secondary">
                  You haven't created any carry runs yet
                </Typography>
                <Typography variant="body2" align="center" color="text.secondary">
                  Click the + button to create your first carry run.
                </Typography>
              </CardContent>
            </Card>
          ) : (
            <Grid container spacing={3}>
              {getFilteredLobbies().map((lobby) => (
                <Grid item xs={12} key={lobby.id}>
                  <LobbyCard
                    lobby={lobby}
                    onLobbyDeleted={handleLobbyDeleted}
                    onSignupChanged={handleSignupChanged}
                  />
                </Grid>
              ))}
            </Grid>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          {getFilteredLobbies().length === 0 ? (
            <Card>
              <CardContent>
                <Typography variant="h6" align="center" color="text.secondary">
                  You haven't joined any carry runs yet
                </Typography>
                <Typography variant="body2" align="center" color="text.secondary">
                  Browse available carries and sign up for passenger slots.
                </Typography>
              </CardContent>
            </Card>
          ) : (
            <Grid container spacing={3}>
              {getFilteredLobbies().map((lobby) => (
                <Grid item xs={12} key={lobby.id}>
                  <LobbyCard
                    lobby={lobby}
                    onLobbyDeleted={handleLobbyDeleted}
                    onSignupChanged={handleSignupChanged}
                  />
                </Grid>
              ))}
            </Grid>
          )}
        </TabPanel>

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </StyledContainer>

      {isOrganizer && (
        <StyledFab
          color="primary"
          aria-label="create lobby"
          onClick={() => setCreateDialogOpen(true)}
        >
          <Add />
        </StyledFab>
      )}

      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogContent>
          <CreateLobbyForm
            onLobbyCreated={handleLobbyCreated}
            onCancel={() => setCreateDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
