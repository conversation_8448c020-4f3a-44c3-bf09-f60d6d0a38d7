/* Discord Message Component Styles - Theme-aware */
.discordMessage {
  background-color: var(--discord-message-bg, rgba(64, 68, 75, 0.3));
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.messageContent {
  font-size: 1rem;
  line-height: 1.5;
  margin: 8px 0;
  word-break: break-word;
  white-space: pre-wrap;
  color: var(--discord-text-color, #dcddde);
}

.imageContainer {
  margin-top: 0;
  border-radius: 8px;
  overflow: hidden;
  max-width: 100%;
}

.messageImage {
  width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
}

.roleMention {
  background-color: rgba(114, 137, 218, 0.1);
  color: var(--discord-mention-color, #00aff4); /* Discord standard link blue */
  border-radius: 3px;
  padding: 0 2px;
  font-weight: 500;
}

/* Hover effect for role mentions */
.roleMention:hover {
  background-color: rgba(114, 137, 218, 0.2);
  text-decoration: underline;
}

/* Timestamp styles */
.timestamp {
  font-size: 0.75rem;
  color: var(--discord-timestamp-color, #72767d);
  margin-top: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .discordMessage {
    padding: 8px;
  }
  
  .messageContent {
    font-size: 0.9rem;
  }
}







