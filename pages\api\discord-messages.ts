// filepath: ./pages/api/discord-messages.ts
import type { NextApiRequest, NextApiResponse } from 'next/types';
import Discord from 'discord.js';
import discordConfig from '../../config/discord';
import { DiscordMessage } from 'src/types/discord';
import discordClientManager, { 
  formatMessageContent, 
  cleanMessageContent, 
  aggressiveCleanMessageContent 
} from '../../lib/discord-client';

// Cache for the latest messages
let cachedMessages: DiscordMessage[] = [];
let lastFetched = 0;

export default async function handler(
  req: NextApiRequest, 
  res: NextApiResponse
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET');
  
  try {
    // Check if we need to fetch new messages
    const now = Date.now();
    if (cachedMessages.length === 0 || now - lastFetched > discordConfig.refreshInterval) {
      await discordClientManager.connect();
      const client = discordClientManager.getClient();
      const guildRoles = discordClientManager.getRoles();
      
      // Array to hold all messages with images
      const allMessagesWithImages: DiscordMessage[] = [];
      
      // Fetch the guild
      const guild = await client.guilds.fetch(discordConfig.guildId);
      if (!guild) {
        throw new Error(`Guild not found with ID: ${discordConfig.guildId}`);
      }
      
      // Fetch messages from each channel in the specified guild
      for (const channelId of discordConfig.channelIds) {
        try {
          // Get the channel from the specific guild
          const channel = await guild.channels.fetch(channelId);
          
          // Skip if channel not found
          if (!channel) {
            console.warn(`Channel not found with ID: ${channelId}`);
            continue;
          }
          
          // Check if channel is a text channel
          const isTextChannel =
            channel.isTextBased && channel.isTextBased() &&
            !(channel.isDMBased && channel.isDMBased());
          if (!isTextChannel) {
            console.warn(`Channel ${channelId} is not a text channel`);
            continue;
          }
          
          // Fetch the latest messages
          // @ts-ignore - We've already checked it's a text channel
          const messages = await channel.messages.fetch({ limit: 10 });

          // Process messages with images
          messages.forEach(msg => {
            // Check for image attachments
            const hasImageAttachment = msg.attachments.size > 0 && 
              msg.attachments.some(attachment => 
                attachment.contentType?.startsWith('image/')
              );
            
            // Check for embedded images in embeds
            const hasEmbeddedImage = msg.embeds && msg.embeds.length > 0 && 
              msg.embeds.some(embed => 
                embed.image || (embed.thumbnail && embed.thumbnail.url)
              );
            
            if (hasImageAttachment || hasEmbeddedImage) {
              // Get the image URL from attachment or embed
              let imageUrl = '';
              
              if (hasImageAttachment) {
                const imageAttachment = msg.attachments.find(
                  attachment => attachment.contentType?.startsWith('image/')
                );
                if (imageAttachment) {
                  imageUrl = imageAttachment.url;
                }
              } else if (hasEmbeddedImage) {
                const embedWithImage = msg.embeds.find(embed => 
                  embed.image || (embed.thumbnail && embed.thumbnail.url)
                );
                if (embedWithImage) {
                  imageUrl = embedWithImage.image?.url || embedWithImage.thumbnail?.url || '';
                }
              }
              
              // Format message content to replace role IDs with names
              let formattedContent = formatMessageContent(msg.content || '');
              
              // Clean the content by removing the image URL and any trailing content
              formattedContent = cleanMessageContent(formattedContent, imageUrl);
              
              // After the first cleaning attempt, apply a more aggressive approach if needed
              if (formattedContent.includes('http')) {
                formattedContent = aggressiveCleanMessageContent(formattedContent);
              }
              
              // Extract role mentions
              const roleMentions: any[] = [];
              if (msg.mentions && msg.mentions.roles) {
                msg.mentions.roles.forEach(role => {
                  const roleData = guildRoles.get(role.id);
                  if (roleData) {
                    roleMentions.push({
                      id: role.id,
                      name: roleData.name,
                      color: roleData.color
                    });
                  }
                });
              }
              
              // Only add messages that have either content or an image
              if (formattedContent.trim() || imageUrl) {
                allMessagesWithImages.push({
                  id: msg.id,
                  content: formattedContent,
                  author: {
                    username: msg.author.username || 'Unknown User',
                    avatarUrl: msg.author.displayAvatarURL() || ''
                  },
                  timestamp: msg.createdTimestamp,
                  imageUrl: imageUrl,
                  // @ts-ignore - We've already checked it's a text channel
                  channelName: channel.name || 'Unknown Channel',
                  channelId: channel.id,
                  roleMentions: roleMentions
                });
              }
            }
          });
        } catch (error) {
          console.error(`Error fetching messages from channel ${channelId}:`, error);
          // Continue with other channels even if one fails
        }
      }
      
      // Sort messages by timestamp (newest first)
      allMessagesWithImages.sort((a, b) => b.timestamp - a.timestamp);
      
      // Update cache
      cachedMessages = allMessagesWithImages;
      lastFetched = now;
    }
    
    // Return the cached messages (limited to most recent 10)
    res.status(200).json({ 
      messages: cachedMessages.slice(0, 10) 
    });
  } catch (error) {
    console.error('Error fetching Discord messages:', error);
    res.status(500).json({ 
      error: 'Failed to fetch Discord messages',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
