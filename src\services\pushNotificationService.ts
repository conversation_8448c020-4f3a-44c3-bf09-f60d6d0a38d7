interface NotificationSettings {
  enabled: boolean;
}

const SETTINGS_KEY = 'notification_settings';
const SERVICE_WORKER_PATH = '/service-worker.js';

class PushNotificationService {
  /**
   * Check if push notifications are supported in this browser
   */
  isSupported(): boolean {
    return 'serviceWorker' in navigator && 'PushManager' in window;
  }

  /**
   * Get the current notification settings from localStorage
   */
  getSettings(): NotificationSettings {
    try {
      const settings = localStorage.getItem(SETTINGS_KEY);
      return settings ? JSON.parse(settings) : { enabled: false };
    } catch (error) {
      console.error('Error reading notification settings:', error);
      return { enabled: false };
    }
  }

  /**
   * Save notification settings to localStorage
   */
  saveSettings(settings: NotificationSettings): void {
    try {
      localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving notification settings:', error);
    }
  }

  /**
   * Request notification permission from the user
   */
  async requestPermission(): Promise<boolean> {
    try {
      if (!this.isSupported()) {
        return false;
      }

      const permission = await Notification.requestPermission();
      return permission === 'granted';
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  /**
   * Register the service worker for push notifications
   */
  async registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
    try {
      if (!this.isSupported()) {
        return null;
      }

      const registration = await navigator.serviceWorker.register(SERVICE_WORKER_PATH);
      console.log('Service Worker registered successfully:', registration);
      return registration;
    } catch (error) {
      console.error('Error registering service worker:', error);
      return null;
    }
  }

  /**
   * Get the current push subscription if it exists
   */
  async getSubscription(): Promise<PushSubscription | null> {
    try {
      if (!this.isSupported()) {
        return null;
      }

      const registration = await navigator.serviceWorker.ready;
      return await registration.pushManager.getSubscription();
    } catch (error) {
      console.error('Error getting push subscription:', error);
      return null;
    }
  }

  /**
   * Subscribe to push notifications
   */
  async subscribe(registration: ServiceWorkerRegistration): Promise<PushSubscription | null> {
    try {
      // Check if already subscribed
      const existingSubscription = await registration.pushManager.getSubscription();
      if (existingSubscription) {
        console.log('Using existing push subscription');
        return existingSubscription;
      }

      // Get the VAPID public key from environment
      const vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_KEY;
      if (!vapidPublicKey) {
        console.error('VAPID public key is missing');
        return null;
      }

      // Convert the VAPID key to the format expected by the browser
      const applicationServerKey = this.urlBase64ToUint8Array(vapidPublicKey);

      console.log('Creating new push subscription');
      
      // Create a new subscription
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey
      });

      console.log('Push notification subscription successful:', subscription);
      
      // Send the subscription to the server immediately
      try {
        const response = await fetch('/api/push-subscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(subscription),
        });
        
        if (response.ok) {
          console.log('Subscription saved on server successfully');
        } else {
          console.error('Failed to save subscription on server');
        }
      } catch (error) {
        console.error('Error saving subscription on server:', error);
      }
      
      return subscription;
    } catch (error) {
      console.error('Error subscribing to push notifications:', error);
      return null;
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribe(): Promise<boolean> {
    try {
      if (!this.isSupported()) {
        return false;
      }

      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.getSubscription();

      if (subscription) {
        const result = await subscription.unsubscribe();
        console.log('Unsubscribed from push notifications:', result);
        return result;
      }

      return true; // Already unsubscribed
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error);
      return false;
    }
  }

  /**
   * Convert a base64 string to a Uint8Array for the applicationServerKey
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    
    return outputArray;
  }
}

// Create a singleton instance
const pushNotificationService = new PushNotificationService();

export { pushNotificationService };
export default pushNotificationService;



