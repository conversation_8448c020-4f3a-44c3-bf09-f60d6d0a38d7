import type { NextApiRequest, NextApiResponse } from 'next/types';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { LobbyService } from '../../../../src/services/lobbyService';

const lobbyService = new LobbyService();

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  const { lobbyId } = req.query;

  if (!lobbyId || typeof lobbyId !== 'string') {
    return res.status(400).json({ 
      success: false, 
      error: 'Invalid lobby ID' 
    });
  }

  try {
    // Get session and validate user
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    const discordUser = {
      id: (session.user as any).discordId || (session.user as any).id || 'unknown',
      username: (session.user as any).username || session.user.name || 'Unknown User',
      avatar: (session.user as any).image || session.user.image || ''
    };

    if (req.method === 'POST') {
      // Kick user from lobby
      const { userId } = req.body;

      if (!userId || typeof userId !== 'string') {
        return res.status(400).json({ 
          success: false, 
          error: 'Invalid user ID' 
        });
      }

      try {
        const result = await lobbyService.kickUserFromLobby(lobbyId, userId, discordUser.id);
        
        if (result.success) {
          return res.status(200).json({ 
            success: true, 
            message: 'User successfully kicked from lobby' 
          });
        } else {
          return res.status(400).json({ 
            success: false, 
            error: result.error 
          });
        }
      } catch (error) {
        console.error('Error kicking user:', error);
        return res.status(500).json({ 
          success: false, 
          error: 'Failed to kick user' 
        });
      }
    }

    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });

  } catch (error) {
    console.error('Error in kick API:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
}
