// Event types
export type LobbyEventType = 
  | 'lobby_created' 
  | 'lobby_updated' 
  | 'lobby_deleted'
  | 'user_joined'
  | 'user_left'
  | 'user_kicked';

export interface LobbyEvent {
  type: LobbyEventType;
  lobbyId: string;
  data?: any;
  timestamp: string;
}

// Store active connections - this will be populated by the SSE endpoint
let connections: Set<any> = new Set();

// Function to add a connection
export function addConnection(connection: any) {
  connections.add(connection);
  console.log(`SSE connection added. Total connections: ${connections.size}`);
}

// Function to remove a connection
export function removeConnection(connection: any) {
  connections.delete(connection);
  console.log(`SSE connection removed. Total connections: ${connections.size}`);
}

// Function to get connection count
export function getConnectionCount() {
  return connections.size;
}

// Function to broadcast events to all connected clients
export function broadcastLobbyEvent(event: LobbyEvent) {
  if (typeof window !== 'undefined') {
    // Don't broadcast on client side
    return;
  }

  console.log(`Broadcasting event: ${event.type} to ${connections.size} connections`);

  if (connections.size === 0) {
    console.warn('No SSE connections available to broadcast to');
    return;
  }

  const eventData = `data: ${JSON.stringify(event)}\n\n`;

  // Send to all connected clients
  const deadConnections = new Set();
  connections.forEach((res) => {
    try {
      res.write(eventData);
    } catch (error) {
      console.error('Error writing to SSE connection:', error);
      // Mark for removal
      deadConnections.add(res);
    }
  });

  // Remove dead connections
  deadConnections.forEach(conn => {
    connections.delete(conn);
  });

  if (deadConnections.size > 0) {
    console.log(`Removed ${deadConnections.size} dead connections. Active connections: ${connections.size}`);
  }
}
