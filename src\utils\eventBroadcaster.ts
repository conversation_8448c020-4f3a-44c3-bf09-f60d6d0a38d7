// Event types
export type LobbyEventType = 
  | 'lobby_created' 
  | 'lobby_updated' 
  | 'lobby_deleted'
  | 'user_joined'
  | 'user_left'
  | 'user_kicked';

export interface LobbyEvent {
  type: LobbyEventType;
  lobbyId: string;
  data?: any;
  timestamp: string;
}

// Store active connections - this will be populated by the SSE endpoint
let connections: Set<any> = new Set();

// Function to set connections from the SSE endpoint
export function setConnections(connectionSet: Set<any>) {
  connections = connectionSet;
}

// Function to broadcast events to all connected clients
export function broadcastLobbyEvent(event: LobbyEvent) {
  if (typeof window !== 'undefined') {
    // Don't broadcast on client side
    return;
  }

  const eventData = `data: ${JSON.stringify(event)}\n\n`;
  
  // Send to all connected clients
  connections.forEach((res) => {
    try {
      res.write(eventData);
    } catch (error) {
      // Remove dead connections
      connections.delete(res);
    }
  });
}
