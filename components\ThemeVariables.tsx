import React, { useEffect } from 'react';
import { useTheme } from '@mui/material/styles';

const ThemeVariables: React.FC = () => {
  const theme = useTheme();

  useEffect(() => {
    // Get the root element to set CSS variables
    const root = document.documentElement;
    
    // Determine which theme scheme is active based on primary color
    const primaryColor = theme.palette.primary.main;
    let themeScheme = 'default';
    
    // Check for NebulaFighterTheme
    if (primaryColor === '#8C7CF0') {
      themeScheme = 'nebula';
    } 
    // Check for DarkSpacesTheme
    else if (primaryColor === '#CB3C1D') {
      themeScheme = 'darkspaces';
    }
    // Check for GreenFieldsTheme
    else if (primaryColor === '#44a574') {
      themeScheme = 'greenfields';
    }
    
    // Set theme-specific Discord section background
    let sectionBg, cardBg, messageBg, textColor, mentionColor;
    
    switch (themeScheme) {
      case 'nebula':
        sectionBg = '#070C27';
        cardBg = 'rgba(17, 22, 51, 0.8)';
        messageBg = 'rgba(43, 48, 77, 0.6)';
        textColor = '#CBCCD2';
        mentionColor = '#8C7CF0';
        break;
      case 'darkspaces':
        sectionBg = '#090A0C';
        cardBg = 'rgba(37, 37, 37, 0.8)';
        messageBg = 'rgba(49, 49, 49, 0.6)';
        textColor = '#FFFFFF';
        mentionColor = '#CB3C1D';
        break;
      case 'greenfields':
        sectionBg = '#141c23';
        cardBg = 'rgba(35, 42, 46, 0.8)';
        messageBg = 'rgba(53, 60, 64, 0.6)';
        textColor = '#CBCCD2';
        mentionColor = '#44a574';
        break;
      default:
        sectionBg = theme.palette.background.default;
        cardBg = `${theme.palette.background.paper}cc`;
        messageBg = `${theme.palette.action.hover}99`;
        textColor = theme.palette.text.primary;
        mentionColor = theme.palette.primary.main;
    }
    
    // Set the CSS variables
    root.style.setProperty('--discord-section-bg', sectionBg);
    root.style.setProperty('--discord-card-bg', cardBg);
    root.style.setProperty('--discord-message-wrapper-bg', messageBg);
    root.style.setProperty('--discord-message-bg', messageBg);
    root.style.setProperty('--discord-text-color', textColor);
    root.style.setProperty('--discord-mention-color', mentionColor);
    root.style.setProperty('--discord-timestamp-color', 
      theme.palette.text.secondary);
    
    // Set border color
    root.style.setProperty(
      '--discord-border-color', 
      theme.palette.mode === 'dark' 
        ? 'rgba(255, 255, 255, 0.1)' 
        : 'rgba(0, 0, 0, 0.1)'
    );

    // Set button background colors
    root.style.setProperty(
      '--discord-button-bg', 
      theme.palette.mode === 'dark' 
        ? 'rgba(114, 137, 218, 0.1)' 
        : 'rgba(114, 137, 218, 0.1)'
    );

    root.style.setProperty(
      '--discord-button-hover-bg', 
      theme.palette.mode === 'dark' 
        ? 'rgba(114, 137, 218, 0.3)' 
        : 'rgba(114, 137, 218, 0.3)'
    );

    // Set dot background color
    root.style.setProperty(
      '--discord-dot-bg', 
      theme.palette.mode === 'dark' 
        ? 'rgba(255, 255, 255, 0.3)' 
        : 'rgba(0, 0, 0, 0.3)'
    );

    // Set author name color - red for all themes
    root.style.setProperty(
      '--discord-author-name-color', 
      '#ff5555' // Red color for author name
    );
    
  }, [theme]);

  return null; // This component doesn't render anything
};

export default ThemeVariables;
