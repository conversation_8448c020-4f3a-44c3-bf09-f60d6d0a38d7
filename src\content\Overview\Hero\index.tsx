import {
  <PERSON>,
  But<PERSON>,
  Container,
  Grid,
  Typography,
  styled
} from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faShieldAlt,
  faExchangeAlt,
  faRobot
} from '@fortawesome/free-solid-svg-icons';

import Link from 'src/components/Link';

const TypographyH1 = styled(Typography)(
  ({ theme }) => `
    font-size: ${theme.typography.pxToRem(50)};
`
);

const TypographyH2 = styled(Typography)(
  ({ theme }) => `
    font-size: ${theme.typography.pxToRem(17)};
`
);

const LabelWrapper = styled(Box)(
  ({ theme }) => `
    background-color: ${theme.colors.success.main};
    color: ${theme.palette.success.contrastText};
    font-weight: bold;
    border-radius: 30px;
    text-transform: uppercase;
    display: inline-block;
    font-size: ${theme.typography.pxToRem(11)};
    padding: ${theme.spacing(0.5)} ${theme.spacing(1.5)};
    margin-bottom: ${theme.spacing(2)};
`
);

// const MuiAvatar = styled(Box)(
//   ({ theme }) => `
//     width: ${theme.spacing(8)};
//     height: ${theme.spacing(8)};
//     border-radius: ${theme.general.borderRadius};
//     background-color: #e5f7ff;
//     flex-shrink: 0;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     margin: 0 auto ${theme.spacing(2)};

//     img {
//       width: 60%;
//       height: 60%;
//       display: block;
//     }
// `
// );

// const TsAvatar = styled(Box)(
//   ({ theme }) => `
//     width: ${theme.spacing(8)};
//     height: ${theme.spacing(8)};
//     border-radius: ${theme.general.borderRadius};
//     background-color: #dfebf6;
//     flex-shrink: 0;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     margin: 0 auto ${theme.spacing(2)};

//     img {
//       width: 60%;
//       height: 60%;
//       display: block;
//     }
// `
// );

// const NextJsAvatar = styled(Box)(
//   ({ theme }) => `
//   width: ${theme.spacing(8)};
//   height: ${theme.spacing(8)};
//   border-radius: ${theme.general.borderRadius};
//   background-color: #dfebf6;
//   flex-shrink: 0;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   margin: 0 auto ${theme.spacing(2)};

//     img {
//       width: 60%;
//       height: 60%;
//       display: block;
//     }
// `
// );

function Hero() {
  return (
    <Container maxWidth="lg" sx={{ textAlign: 'center' }}>
      <Grid
        spacing={{ xs: 6, md: 10 }}
        justifyContent="center"
        alignItems="center"
        container
      >
        <Grid item md={10} lg={8} mx="auto">
          <LabelWrapper color="success">Version 0.1</LabelWrapper>
          <TypographyH1 sx={{ mb: 2 }} variant="h1">
            Lost Ark Pals Official Website
          </TypographyH1>
          <TypographyH2
            sx={{ lineHeight: 1.5, pb: 4 }}
            variant="h4"
            color="text.secondary"
            fontWeight="normal"
          >
            Lost Ark Pals is a Lost Ark Community dedicated to offering busses in the game Lost Ark
          </TypographyH2>
          <Button
            component={Link}
            href="https://discord.gg/loapals"
            size="large"
            variant="contained"
          >
            Join our discord
          </Button>
          <Button
            sx={{ ml: 2 }}
            component="a"
            target="_blank"
            rel="noopener"
            href="https://dyno.gg/form/4c31ffd7"
            size="large"
            variant="text"
          >
            Request a bus
          </Button>
          {/*<Grid container spacing={3} mt={5}>
            <Grid item md={4}>
              <Box sx={{ pb: 2 }}>
                <FontAwesomeIcon icon={faShieldAlt} size="4x" />
              </Box>
              <Typography variant="h4">
                <Box sx={{ pb: 2 }}>
                  <b>Bussing services</b>
                </Box>
                <Typography component="span" variant="subtitle2">
                  Lost Ark Pals is fully committed to the Discord and Lost Ark terms of
                  service, to avoid any potential harm to anyone's account.
                </Typography>
              </Typography>
            </Grid>
           <Grid item md={4}>
              <Box sx={{ pb: 2 }}>
                <FontAwesomeIcon icon={faExchangeAlt} size="4x" />
              </Box>
              <Typography variant="h4">
                <Box sx={{ pb: 2 }}>
                  <b>Make gold</b>
                </Box>
                <Typography component="span" variant="subtitle2">
                  Selling Mordum Hardmode Title "Lord of Thunder". Join our discord for requests! We have over 200+ runs completed every month, with over 500 monthly active users.
                </Typography>
              </Typography>
            </Grid>
            <Grid item md={4}>
              <Box sx={{ pb: 2 }}>
                <FontAwesomeIcon icon={faRobot} size="4x" />
              </Box>
              <Typography variant="h4">
                <Box sx={{ pb: 2 }}>
                  <b>Accessible</b>
                </Box>
                <Typography component="span" variant="subtitle2">
                  We offer state of the art tools for easy matchmaking between buyers and sellers of bus services.
                </Typography>
              </Typography>
            </Grid>
          </Grid>*/}
        </Grid>
      </Grid>
    </Container>
  );
}

export default Hero;
