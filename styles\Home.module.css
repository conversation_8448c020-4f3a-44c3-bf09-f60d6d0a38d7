/* Discord Feed Styles - Theme-aware */
.discordSection {
  background-color: var(--discord-section-bg, #2c2f33);
  background-image: url('/images/discord-bg.jpg');
  background-size: cover;
  background-position: center;
  background-blend-mode: overlay;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Discord Card and Loading State Styles */
.discordCard {
  background-color: var(--discord-card-bg, rgba(47, 49, 54, 0.8));
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin: 0 auto;
  color: var(--discord-text-color, #ffffff);
  min-height: 300px; /* Set minimum height to prevent resizing */
  display: flex;
  flex-direction: column;
}

.discordMessageWrapper {
  background-color: var(--discord-message-wrapper-bg, rgba(64, 68, 75, 0.6));
  border-radius: 8px;
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Discord Header and Navigation Styles */
.discordHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.discordAuthor {
  display: flex;
  align-items: center;
}

.authorAvatar {
  border-radius: 50%;
  margin-right: 0.75rem; /* Increased spacing between avatar and name */
}

.authorName {
  color: var(--discord-author-name-color, #ff5555); /* Red color for author name */
  font-weight: bold;
}

.discordMeta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.discordNavigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 0.5rem;
  border-top: 1px solid var(--discord-border-color, rgba(255, 255, 255, 0.1));
}

.navButton {
  background-color: var(--discord-button-bg, rgba(114, 137, 218, 0.1));
  color: var(--discord-text-color, #dcddde);
  border: none;
  border-radius: 4px;
  padding: 4px 12px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.navButton:hover {
  background-color: var(--discord-button-hover-bg, rgba(114, 137, 218, 0.3));
}

.navDots {
  display: flex;
  align-items: center;
}

.navDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--discord-dot-bg, rgba(255, 255, 255, 0.3));
  margin: 0 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.activeDot {
  background-color: var(--discord-mention-color, #00aff4);
}

.roleMention {
  background-color: rgba(114, 137, 218, 0.1);
  color: #7289da;
  border-radius: 3px;
  padding: 0 2px;
  font-weight: 500;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: var(--discord-text-color, #dcddde);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px; /* Match the approximate height of a message */
  flex: 1;
}

.loadingSpinner {
  margin-bottom: 1rem;
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border-top-color: var(--discord-mention-color, #00aff4);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.noMessage {
  text-align: center;
  padding: 2rem;
  color: var(--discord-text-color, #dcddde);
  min-height: 200px; /* Match the approximate height of a message */
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .discordCard {
    width: 100%;
  }
  
  .discordHeader {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .discordMeta {
    margin-top: 0.5rem;
    align-items: flex-start;
  }
  
  .navButton {
    padding: 3px 8px;
    font-size: 0.9rem;
  }
}

/* Remove the :root selector and CSS variables from here */
