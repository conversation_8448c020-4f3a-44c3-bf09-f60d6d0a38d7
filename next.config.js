// CommonJS module syntax
const withImages = require('next-images');
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});
const withTM = require('next-transpile-modules')([
  'react-apexcharts',
  'apexcharts',
]);

// Define redirects
const redirects = {
  async redirects() {
    return [
      {
        source: '/dashboards',
        destination: '/dashboards/tasks',
        permanent: true
      },
      {
        source: '/discord',
        destination: 'https://discord.gg/loapals',
        permanent: true
      }
    ];
  }
};

// Add Discord CDN to allowed image domains
const nextConfig = {
  images: {
    domains: [
      'cdn.discordapp.com', 
      'media.discordapp.net',
      'i.imgur.com',
      'images-ext-1.discordapp.net',
      'images-ext-2.discordapp.net'
    ]
  },
  // Optimize package imports for better performance
  experimental: {
    optimizePackageImports: [
      '@mui/material',
      '@mui/icons-material',
      '@mui/lab',
      '@fortawesome/free-solid-svg-icons',
      'date-fns'
    ]
  },
  // Opt specific packages out of bundling
  serverExternalPackages: [
    'mongodb',
    'web-push',
    'discord.js'
  ]
};

// Combine configurations and export
const config = withBundleAnalyzer(withTM(withImages({
  ...redirects,
  ...nextConfig
})));

// Export the configuration
module.exports = config;
