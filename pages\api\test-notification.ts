import type { NextApiRequest, NextApiResponse } from 'next/types';
import webpush from 'web-push';

// Configure web-push with VAPID keys
webpush.setVapidDetails(
  'mailto:<EMAIL>', // Replace with your email
  process.env.NEXT_PUBLIC_VAPID_KEY || '',
  process.env.VAPID_PRIVATE_KEY || ''
);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { subscription } = req.body;
    
    if (!subscription) {
      return res.status(400).json({ error: 'No subscription provided' });
    }

    console.log('Sending test notification to subscription');
    
    const payload = JSON.stringify({
      title: 'Test Notification',
      content: 'This is a test notification to verify push notifications are working.',
      timestamp: Date.now(),
      url: '/notifications'
    });

    await webpush.sendNotification(subscription, payload);
    
    return res.status(200).json({
      success: true,
      message: 'Test notification sent successfully'
    });
  } catch (error) {
    console.error('Error sending test notification:', error);
    return res.status(500).json({ 
      error: 'Failed to send test notification',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
