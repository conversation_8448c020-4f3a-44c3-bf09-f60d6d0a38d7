export interface DiscordUser {
  id: string;
  username: string;
  discriminator: string;
  avatar?: string;
  email?: string;
}

export interface RaidSlot {
  position: number; // 1-8
  type: 'organizer' | 'driver' | 'passenger';
  user?: DiscordUser;
  signedUpAt?: Date;
}

export interface GameLobby {
  _id?: string;
  id: string;
  title: string;
  description?: string;
  organizerId: string; // Discord user ID
  organizer: DiscordUser;
  scheduledTime: Date;
  maxPassengers: number; // 1-7 (8 total slots - 1 organizer = max 7 passengers)
  slots: RaidSlot[]; // Always 8 slots total
  status: 'open' | 'full' | 'started' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

export interface LobbySignup {
  lobbyId: string;
  userId: string; // Discord user ID
  user: DiscordUser;
  slotPosition: number;
  signedUpAt: Date;
}

export interface OrganizerConfig {
  discordIds: string[];
  lastUpdated: Date;
}

// API Request/Response types
export interface CreateLobbyRequest {
  title: string;
  description?: string;
  scheduledTime: string; // ISO string
  maxPassengers: number;
}

export interface CreateLobbyResponse {
  success: boolean;
  lobby?: GameLobby;
  error?: string;
}

export interface SignupRequest {
  lobbyId: string;
  slotPosition: number;
}

export interface SignupResponse {
  success: boolean;
  signup?: LobbySignup;
  error?: string;
}

export interface LobbiesResponse {
  success: boolean;
  lobbies?: GameLobby[];
  error?: string;
}

export interface LobbyResponse {
  success: boolean;
  lobby?: GameLobby;
  error?: string;
}
