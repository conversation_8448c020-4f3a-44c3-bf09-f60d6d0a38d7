import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    accessToken?: string
    user: {
      id: string
      discordId?: string
      name?: string | null
      email?: string | null
      image?: string | null
    }
  }

  interface Profile {
    id: string
    username: string
    discriminator: string
    avatar?: string
    email?: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    accessToken?: string
    id?: string
    discordId?: string
  }
}
