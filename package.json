{"name": "tokyo-free-black-nextjs-admin-dashboard", "version": "1.0.0", "title": "Tokyo Free Black NextJS Typescript Admin Dashboard", "description": "High performance React template built with lots of powerful MUI (Material-UI) components across multiple product niches for fast & perfect apps development processes", "author": {"name": "BloomUI.com", "url": "https://bloomui.com"}, "private": false, "dependencies": {"@emotion/cache": "11.7.1", "@emotion/react": "11.9.0", "@emotion/server": "11.4.0", "@emotion/styled": "11.8.1", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@mui/core": "5.0.0-alpha.54", "@mui/icons-material": "5.8.2", "@mui/lab": "5.0.0-alpha.84", "@mui/material": "5.8.2", "@mui/styles": "5.8.0", "@next/bundle-analyzer": "^12.3.4", "@types/node": "17.0.39", "@types/nprogress": "0.2.0", "@types/numeral": "2.0.2", "@types/react": "17.0.40", "@types/react-dom": "17.0.13", "@types/web-push": "^3.6.3", "apexcharts": "3.35.3", "clsx": "1.1.1", "context-api": "0.0.2", "date-fns": "2.28.0", "discord.js": "^14.19.3", "mongodb": "^6.17.0", "next": "^12.1.6", "next-auth": "^4.24.11", "next-images": "1.8.4", "next-sitemap": "^4.2.3", "next-transpile-modules": "9.0.0", "nprogress": "0.2.0", "numeral": "2.0.6", "react": "17.0.2", "react-apexcharts": "1.4.0", "react-custom-scrollbars-2": "4.4.0", "react-dom": "17.0.2", "typescript": "^5.8.3", "web-push": "^3.6.7"}, "scripts": {"dev": "next", "build": "next build", "start": "next start", "export": "next export", "lint": "next lint", "lint-fix": "next lint --fix", "format": "prettier --write \"./**/*.{ts,tsx,js,jsx,json}\" --config ./.prettierrc", "postbuild": "next-sitemap", "analyze": "ANALYZE=true next build"}, "devDependencies": {"eslint": "8.17.0", "eslint-config-next": "12.1.6", "eslint-config-prettier": "8.5.0", "eslint-plugin-prettier": "4.0.0", "prettier": "2.6.2"}}