import { MongoClient, Db, MongoClientOptions } from 'mongodb';

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/lostarkpals';
const MONGODB_DB = process.env.MONGODB_DB || 'lostarkpals';

// Check the MongoDB URI
if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable');
}

// Check the MongoDB DB
if (!MONGODB_DB) {
  throw new Error('Please define the MONGODB_DB environment variable');
}

/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */
// Define a proper interface for the global MongoDB cache
interface MongoDBCache {
  conn: { client: MongoClient; db: Db } | null;
  promise: Promise<{ client: MongoClient; db: Db }> | null;
}

// Define the global type with our cache interface
declare global {
  var mongo: MongoDBCache;
}

// Initialize the cache if it doesn't exist
let cached: MongoDBCache = global.mongo || { conn: null, promise: null };

if (!global.mongo) {
  global.mongo = cached;
}

export async function connectToDatabase() {
  if (cached.conn) {
    return cached.conn;
  }

  if (!cached.promise) {
    // Updated options for MongoDB 6.x with simplified TLS settings
    const opts: MongoClientOptions = {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      // Add Stable API version for better compatibility
      serverApi: {
        version: '1',
        strict: true,
        deprecationErrors: true
      }
    };

    console.log('Connecting to MongoDB...');
    
    cached.promise = MongoClient.connect(MONGODB_URI, opts)
      .then((client) => {
        console.log('Connected to MongoDB successfully');
        return {
          client,
          db: client.db(MONGODB_DB),
        };
      })
      .catch((err) => {
        console.error('Failed to connect to MongoDB:', err);
        // Add more detailed error logging
        if (err.name === 'MongoServerSelectionError') {
          console.error('MongoDB server selection error details:', {
            message: err.message,
            cause: err.cause ? err.cause.message : 'No cause specified',
            code: err.code || 'No error code'
          });
        }
        cached.promise = null;
        throw err;
      });
  }

  try {
    cached.conn = await cached.promise;
    return cached.conn;
  } catch (e) {
    cached.promise = null;
    console.error('Error in connectToDatabase:', e);
    throw e;
  }
}





