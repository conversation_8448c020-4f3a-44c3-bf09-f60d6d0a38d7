import type { NextApiRequest, NextApiResponse } from 'next/types';
import { connectToDatabase } from '../../lib/mongodb';
import webpush from 'web-push';

// Configure web-push with VAPID keys
webpush.setVapidDetails(
  'mailto:<EMAIL>', // Replace with your email
  process.env.NEXT_PUBLIC_VAPID_KEY || '',
  process.env.VAPID_PRIVATE_KEY || ''
);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Verify API key for security
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ') || authHeader.split(' ')[1] !== process.env.API_SECRET_KEY) {
    console.error('Unauthorized test notification request');
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    console.log('Processing test notification');

    let db;
    try {
      // Connect to the database
      const connection = await connectToDatabase();
      db = connection.db;
    } catch (dbError) {
      console.error('MongoDB connection error:', dbError);
      // Return success but with 0 notifications sent
      return res.status(200).json({
        success: true,
        sent: 0,
        total: 0,
        error: 'Database connection failed'
      });
    }
    
    // Get all active subscriptions
    const subscriptions = await db.collection('push_subscriptions')
      .find({})
      .toArray();

    console.log(`Found ${subscriptions.length} push subscriptions`);

    if (!subscriptions || subscriptions.length === 0) {
      return res.status(200).json({
        success: true,
        sent: 0,
        total: 0,
        message: 'No subscriptions found'
      });
    }

    // Send test notifications to all subscribers
    const notificationPromises = subscriptions.map(async (sub) => {
      try {
        const payload = JSON.stringify({
          title: 'Test Notification',
          content: 'This is a test notification from the server.',
          timestamp: Date.now(),
          url: '/discord'
        });

        console.log(`Sending test notification to subscription: ${sub.subscription.endpoint.substring(0, 30)}...`);
        
        await webpush.sendNotification(
          sub.subscription,
          payload
        );
        return { success: true, endpoint: sub.subscription.endpoint };
      } catch (error) {
        console.error('Error sending test notification:', error);
        return { success: false, endpoint: sub.subscription.endpoint, error: error instanceof Error ? error.message : 'Unknown error' };
      }
    });

    const results = await Promise.all(notificationPromises);
    const successCount = results.filter(r => r.success).length;

    console.log(`Successfully sent ${successCount} out of ${subscriptions.length} test notifications`);

    return res.status(200).json({
      success: true,
      sent: successCount,
      total: subscriptions.length,
      results: results
    });
  } catch (error) {
    console.error('Error in test-notification-send API:', error);
    return res.status(500).json({ 
      error: 'Failed to send test notifications',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}