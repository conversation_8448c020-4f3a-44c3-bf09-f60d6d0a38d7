// Discord bot configuration
interface DiscordConfig {
  token: string;
  guildId: string;
  channelIds: string[];
  refreshInterval: number;
}

const discordConfig: DiscordConfig = {
  // Your Discord bot token (already set up in your environment)
  token: process.env.DISCORD_BOT_TOKEN || '',
  
  // The specific guild (server) ID to fetch from
  guildId: process.env.DISCORD_GUILD_ID || '',
  
  // List of channel IDs to fetch messages from (all within the same guild)
  channelIds: [
    process.env.DISCORD_CHANNEL_ID_1,
    process.env.DISCORD_CHANNEL_ID_2,
    process.env.DISCORD_CHANNEL_ID_3,
    process.env.DISCORD_CHANNEL_ID_4,
    process.env.DISCORD_CHANNEL_ID_5,
    process.env.DISCORD_CHANNEL_ID_6,
    // Add more channels as needed
  ].filter(Boolean) as string[], // Filter out any undefined channels
  
  // How often to check for new messages (in milliseconds)
  refreshInterval: 30000, // 30 seconds
};

export default discordConfig;
