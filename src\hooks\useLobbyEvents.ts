import { useEffect, useRef, useCallback } from 'react';
import { useSession } from 'next-auth/react';

export type LobbyEventType = 
  | 'lobby_created' 
  | 'lobby_updated' 
  | 'lobby_deleted'
  | 'user_joined'
  | 'user_left'
  | 'user_kicked'
  | 'connected'
  | 'heartbeat';

export interface LobbyEvent {
  type: LobbyEventType;
  lobbyId?: string;
  data?: any;
  timestamp: string;
  message?: string;
}

export interface UseLobbyEventsOptions {
  onLobbyCreated?: (event: LobbyEvent) => void;
  onLobbyUpdated?: (event: LobbyEvent) => void;
  onLobbyDeleted?: (event: LobbyEvent) => void;
  onUserJoined?: (event: LobbyEvent) => void;
  onUserLeft?: (event: LobbyEvent) => void;
  onUserKicked?: (event: LobbyEvent) => void;
  onConnected?: (event: LobbyEvent) => void;
  onError?: (error: Error) => void;
}

export function useLobbyEvents(options: UseLobbyEventsOptions = {}) {
  const { data: session } = useSession();
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isConnectedRef = useRef(false);

  const {
    onLobbyCreated,
    onLobbyUpdated,
    onLobbyDeleted,
    onUserJoined,
    onUserLeft,
    onUserKicked,
    onConnected,
    onError
  } = options;

  const handleEvent = useCallback((event: LobbyEvent) => {
    console.log('Received lobby event:', event);

    switch (event.type) {
      case 'lobby_created':
        console.log('Handling lobby_created event');
        onLobbyCreated?.(event);
        break;
      case 'lobby_updated':
        console.log('Handling lobby_updated event');
        onLobbyUpdated?.(event);
        break;
      case 'lobby_deleted':
        console.log('Handling lobby_deleted event');
        onLobbyDeleted?.(event);
        break;
      case 'user_joined':
        console.log('Handling user_joined event');
        onUserJoined?.(event);
        break;
      case 'user_left':
        console.log('Handling user_left event');
        onUserLeft?.(event);
        break;
      case 'user_kicked':
        console.log('Handling user_kicked event');
        onUserKicked?.(event);
        break;
      case 'connected':
        console.log('SSE connection established');
        onConnected?.(event);
        break;
      case 'heartbeat':
        // Keep connection alive, no action needed
        break;
      default:
        console.log('Unknown event type:', event.type);
    }
  }, [onLobbyCreated, onLobbyUpdated, onLobbyDeleted, onUserJoined, onUserLeft, onUserKicked, onConnected]);

  const connect = useCallback(() => {
    if (!session?.user) {
      console.log('No session, skipping SSE connection');
      return;
    }

    if (isConnectedRef.current) {
      console.log('Already connected to SSE');
      return;
    }

    console.log('Attempting to connect to lobby events SSE...');

    try {
      const eventSource = new EventSource('/api/lobbies/events');
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('SSE connection opened successfully');
        isConnectedRef.current = true;
        // Clear any pending reconnection attempts
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
      };

      eventSource.onmessage = (event) => {
        try {
          const lobbyEvent: LobbyEvent = JSON.parse(event.data);
          handleEvent(lobbyEvent);
        } catch (error) {
          console.error('Error parsing lobby event:', error);
          onError?.(error as Error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('Lobby events connection error:', error);
        isConnectedRef.current = false;
        
        // Close the current connection
        eventSource.close();
        eventSourceRef.current = null;
        
        // Attempt to reconnect after a delay
        if (!reconnectTimeoutRef.current) {
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectTimeoutRef.current = null;
            connect();
          }, 5000); // Reconnect after 5 seconds
        }
        
        onError?.(new Error('Connection lost, attempting to reconnect...'));
      };

    } catch (error) {
      console.error('Error creating EventSource:', error);
      onError?.(error as Error);
    }
  }, [session, handleEvent, onError]);

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    isConnectedRef.current = false;
  }, []);

  // Connect when session is available
  useEffect(() => {
    if (session?.user) {
      connect();
    } else {
      disconnect();
    }

    // Cleanup on unmount
    return () => {
      disconnect();
    };
  }, [session?.user?.id]); // Only depend on user ID to avoid reconnections

  // Handle page visibility changes to reconnect when page becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && session?.user && !isConnectedRef.current) {
        connect();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [session, connect]);

  return {
    isConnected: isConnectedRef.current,
    connect,
    disconnect
  };
}
