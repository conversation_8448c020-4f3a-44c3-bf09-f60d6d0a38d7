import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON>H<PERSON>er,
  CardContent,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Alert,
  CircularProgress,
  Grid
} from '@mui/material';
import { DateTimePicker } from '@mui/lab';
import { useLobby } from '../../hooks/useLobby';
import { CreateLobbyRequest, GameLobby } from '../../types/lobby';

interface CreateLobbyFormProps {
  onLobbyCreated?: (lobby: GameLobby) => void;
  onCancel?: () => void;
}

export default function CreateLobbyForm({ onLobbyCreated, onCancel }: CreateLobbyFormProps) {
  const { createLobby, loading, error } = useLobby();
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    scheduledTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Default to tomorrow
    maxPassengers: 4
  });

  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const handleInputChange = (field: keyof typeof formData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
  ) => {
    const value = event.target ? event.target.value : event;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear validation errors when user starts typing
    if (validationErrors.length > 0) {
      setValidationErrors([]);
    }
  };

  const validateForm = (): boolean => {
    const errors: string[] = [];

    if (!formData.title.trim()) {
      errors.push('Title is required');
    } else if (formData.title.length > 100) {
      errors.push('Title must be 100 characters or less');
    }

    if (formData.scheduledTime <= new Date()) {
      errors.push('Scheduled time must be in the future');
    }

    if (formData.maxPassengers < 1 || formData.maxPassengers > 7) {
      errors.push('Number of passengers must be between 1 and 7');
    }

    setValidationErrors(errors);
    return errors.length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const lobbyRequest: CreateLobbyRequest = {
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        scheduledTime: formData.scheduledTime.toISOString(),
        maxPassengers: formData.maxPassengers
      };

      const newLobby = await createLobby(lobbyRequest);
      
      if (onLobbyCreated) {
        onLobbyCreated(newLobby);
      }

      // Reset form
      setFormData({
        title: '',
        description: '',
        scheduledTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
        maxPassengers: 4
      });
    } catch (err) {
      // Error is handled by the hook
      console.error('Failed to create lobby:', err);
    }
  };

  return (
    <Card>
      <CardHeader
        title="Create New Carry Run"
        subheader="Set up a new carry run for passengers to join"
      />
      <CardContent>
        <Box component="form" onSubmit={handleSubmit} noValidate>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Lobby Title"
                value={formData.title}
                onChange={handleInputChange('title')}
                required
                error={validationErrors.some(err => err.includes('Title'))}
                helperText="Give your lobby a descriptive name"
                disabled={loading}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={handleInputChange('description')}
                multiline
                rows={3}
                helperText="Optional: Add details about the game session"
                disabled={loading}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <DateTimePicker
                label="Scheduled Time"
                value={formData.scheduledTime}
                onChange={handleInputChange('scheduledTime')}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    required
                    error={validationErrors.some(err => err.includes('time'))}
                    disabled={loading}
                  />
                )}
                minDateTime={new Date()}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth required disabled={loading}>
                <InputLabel>Number of Passengers</InputLabel>
                <Select
                  value={formData.maxPassengers}
                  onChange={handleInputChange('maxPassengers')}
                  label="Number of Passengers"
                >
                  {[1, 2, 3, 4, 5, 6, 7].map(num => (
                    <MenuItem key={num} value={num}>
                      {num} Passenger{num !== 1 ? 's' : ''}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {(validationErrors.length > 0 || error) && (
              <Grid item xs={12}>
                <Alert severity="error">
                  {error && <Typography>{error}</Typography>}
                  {validationErrors.map((err, index) => (
                    <Typography key={index}>{err}</Typography>
                  ))}
                </Alert>
              </Grid>
            )}

            <Grid item xs={12}>
              <Box display="flex" gap={2} justifyContent="flex-end">
                {onCancel && (
                  <Button
                    variant="outlined"
                    onClick={onCancel}
                    disabled={loading}
                  >
                    Cancel
                  </Button>
                )}
                <Button
                  type="submit"
                  variant="contained"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : null}
                >
                  {loading ? 'Creating...' : 'Create Lobby'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
}
