import { useState } from 'react';
import Head from 'next/head';
import type { ReactElement } from 'react';
import SidebarLayout from 'src/layouts/SidebarLayout';
import {
  Container,
  Grid,
  Card,
  CardHeader,
  CardContent,
  Divider,
  Typography,
  Box,
  Tabs,
  Tab,
  Button
} from '@mui/material';
import NotificationSettings from 'src/components/NotificationSettings';
import { useAuth } from 'src/contexts/AuthContext';

// NextAuth types are defined in types/next-auth.d.ts

function UserSettings() {
  const [activeTab, setActiveTab] = useState<string>('notifications');
  const { user, isAuthenticated, login } = useAuth();

  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    setActiveTab(newValue);
  };

  return (
    <>
      <Head>
        <title>User Settings - Lost Ark Pals</title>
      </Head>
      <Container maxWidth="lg" sx={{ mt: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardHeader title="User Settings" />
              <Divider />
              <CardContent>
                {!isAuthenticated ? (
                  <Box textAlign="center" py={3}>
                    <Typography variant="h6" gutterBottom>
                      Please log in to access all settings
                    </Typography>
                    <Button 
                      variant="contained" 
                      color="primary" 
                      onClick={login}
                      sx={{ mt: 2 }}
                    >
                      Login with Discord
                    </Button>
                  </Box>
                ) : (
                  <>
                    <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                      <Tabs
                        value={activeTab}
                        onChange={handleTabChange}
                        aria-label="settings tabs"
                      >
                        <Tab label="Notifications" value="notifications" />
                        <Tab label="Account" value="account" />
                        <Tab label="Privacy" value="privacy" />
                      </Tabs>
                    </Box>

                    {activeTab === 'notifications' && (
                      <Box>
                        <NotificationSettings userId={user?.id} />
                      </Box>
                    )}

                    {activeTab === 'account' && (
                      <Box>
                        <Typography variant="h6" gutterBottom>
                          Account Information
                        </Typography>
                        <Typography variant="body1">
                          Username: {user?.username}
                        </Typography>
                        <Typography variant="body1">
                          Discord ID: {user?.id}
                        </Typography>
                        {user?.email && (
                          <Typography variant="body1">
                            Email: {user.email}
                          </Typography>
                        )}
                      </Box>
                    )}

                    {activeTab === 'privacy' && (
                      <Box>
                        <Typography variant="h6" gutterBottom>
                          Privacy Settings
                        </Typography>
                        <Typography variant="body1" paragraph>
                          Manage how your data is used on Lost Ark Pals.
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Additional privacy settings will be available soon.
                        </Typography>
                      </Box>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </>
  );
}

UserSettings.getLayout = function getLayout(page: ReactElement) {
  return <SidebarLayout>{page}</SidebarLayout>;
};

export default UserSettings;


