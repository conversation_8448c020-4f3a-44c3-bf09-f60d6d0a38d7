import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { Box, Container, Typography, CircularProgress } from '@mui/material';
import Head from 'next/head';
import type { ReactElement } from 'react';
import BaseLayout from 'src/layouts/BaseLayout';

function DiscordRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to Discord
    window.location.href = 'https://discord.gg/loapals';
  }, []);

  return (
    <>
      <Head>
        <title>Redirecting to Discord - Lost Ark Pals</title>
      </Head>
      <Container sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100vh' }}>
        <CircularProgress size={60} />
        <Typography variant="h4" sx={{ mt: 4 }}>
          Redirecting to Discord...
        </Typography>
        <Typography variant="body1" sx={{ mt: 2 }}>
          If you are not redirected automatically, please click{' '}
          <a href="https://discord.gg/loapals" style={{ color: 'inherit', fontWeight: 'bold' }}>
            here
          </a>
          .
        </Typography>
      </Container>
    </>
  );
}

export default DiscordRedirect;

DiscordRedirect.getLayout = function getLayout(page: ReactElement) {
  return <BaseLayout>{page}</BaseLayout>;
};