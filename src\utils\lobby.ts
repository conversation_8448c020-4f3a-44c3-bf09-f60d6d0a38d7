import { GameLobby, RaidSlot, DiscordUser } from '../types/lobby';

export function generateLobbyId(): string {
  return `lobby_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function initializeRaidSlots(maxPassengers: number, organizer: DiscordUser): RaidSlot[] {
  const slots: RaidSlot[] = [];

  // Slot 1: Always the organizer (driver)
  slots.push({
    position: 1,
    type: 'organizer',
    user: organizer,
    signedUpAt: new Date()
  });

  // Driver slots: Fill remaining driver positions with anonymous drivers
  const numDrivers = 8 - maxPassengers; // Total drivers = 8 - passengers
  for (let i = 2; i <= numDrivers; i++) {
    slots.push({
      position: i,
      type: 'driver',
      user: undefined, // Anonymous driver
      signedUpAt: undefined
    });
  }

  // Passenger slots: Available for signup
  for (let i = numDrivers + 1; i <= 8; i++) {
    slots.push({
      position: i,
      type: 'passenger',
      user: undefined,
      signedUpAt: undefined
    });
  }

  return slots;
}

export function isLobbyFull(lobby: GameLobby): boolean {
  const passengerSlots = lobby.slots.filter(slot => slot.type === 'passenger');
  const filledPassengerSlots = passengerSlots.filter(slot => slot.user);
  return filledPassengerSlots.length >= lobby.maxPassengers;
}

export function getAvailablePassengerPositions(lobby: GameLobby): number[] {
  return lobby.slots
    .filter(slot => slot.type === 'passenger' && !slot.user)
    .map(slot => slot.position);
}

export function isUserSignedUp(lobby: GameLobby, userId: string): boolean {
  return lobby.slots.some(slot => slot.user?.id === userId);
}

export function getUserSlotPosition(lobby: GameLobby, userId: string): number | null {
  const slot = lobby.slots.find(slot => slot.user?.id === userId);
  return slot ? slot.position : null;
}

export function getPassengerSlots(lobby: GameLobby): RaidSlot[] {
  return lobby.slots.filter(slot => slot.type === 'passenger');
}

export function getDriverSlots(lobby: GameLobby): RaidSlot[] {
  return lobby.slots.filter(slot => slot.type === 'driver' || slot.type === 'organizer');
}

export function canUserSignUp(lobby: GameLobby, userId: string, position: number): {
  canSignUp: boolean;
  reason?: string;
} {
  // Check if lobby is open
  if (lobby.status !== 'open') {
    return { canSignUp: false, reason: 'Lobby is not open for signups' };
  }

  // Check if user is already signed up
  if (isUserSignedUp(lobby, userId)) {
    return { canSignUp: false, reason: 'You are already signed up for this lobby' };
  }

  // Check if position is valid and is a passenger slot
  const slot = lobby.slots.find(s => s.position === position);
  if (!slot) {
    return { canSignUp: false, reason: 'Invalid slot position' };
  }

  if (slot.type !== 'passenger') {
    return { canSignUp: false, reason: 'You can only sign up for passenger slots' };
  }

  // Check if position is available
  if (slot.user) {
    return { canSignUp: false, reason: 'Passenger slot is already taken' };
  }

  // Check if lobby is full
  if (isLobbyFull(lobby)) {
    return { canSignUp: false, reason: 'Lobby is full' };
  }

  return { canSignUp: true };
}

export function validateLobbyData(data: {
  title: string;
  scheduledTime: string;
  maxPassengers: number;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate title
  if (!data.title || data.title.trim().length === 0) {
    errors.push('Title is required');
  } else if (data.title.length > 100) {
    errors.push('Title must be 100 characters or less');
  }

  // Validate scheduled time
  const scheduledTime = new Date(data.scheduledTime);
  if (isNaN(scheduledTime.getTime())) {
    errors.push('Invalid scheduled time');
  } else if (scheduledTime <= new Date()) {
    errors.push('Scheduled time must be in the future');
  }

  // Validate max passengers (1-7, since organizer takes 1 slot and we need at least 1 driver)
  if (!Number.isInteger(data.maxPassengers) || data.maxPassengers < 1 || data.maxPassengers > 7) {
    errors.push('Number of passengers must be between 1 and 7');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function formatLobbyTime(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short'
  }).format(date);
}

export function getLobbyStatusColor(status: GameLobby['status']): string {
  switch (status) {
    case 'open':
      return '#4caf50'; // green
    case 'full':
      return '#ff9800'; // orange
    case 'started':
      return '#2196f3'; // blue
    case 'completed':
      return '#9e9e9e'; // gray
    case 'cancelled':
      return '#f44336'; // red
    default:
      return '#9e9e9e';
  }
}

export function updateLobbyStatus(lobby: GameLobby): GameLobby['status'] {
  const now = new Date();
  const scheduledTime = new Date(lobby.scheduledTime);

  // If lobby is cancelled, keep it cancelled
  if (lobby.status === 'cancelled') {
    return 'cancelled';
  }

  // If lobby is completed, keep it completed
  if (lobby.status === 'completed') {
    return 'completed';
  }

  // If scheduled time has passed, mark as started or completed
  if (scheduledTime <= now) {
    return lobby.status === 'started' ? 'started' : 'started';
  }

  // If lobby is full, mark as full
  if (isLobbyFull(lobby)) {
    return 'full';
  }

  // Otherwise, lobby is open
  return 'open';
}

export function getDisplayName(
  slot: RaidSlot,
  viewerUserId: string,
  isViewerOrganizer: boolean
): string {
  // Driver slots always show as "Driver"
  if (slot.type === 'driver' && !slot.user) {
    return 'Driver';
  }

  // Organizer slot - always visible to everyone
  if (slot.type === 'organizer' && slot.user) {
    return slot.user.username;
  }

  // Passenger slots
  if (slot.type === 'passenger') {
    if (!slot.user) {
      return 'Open';
    }

    // Show real name if viewer is organizer or if it's the viewer's own slot
    if (isViewerOrganizer || slot.user.id === viewerUserId) {
      return slot.user.username;
    }

    // Otherwise show as anonymous
    return 'Anonymous Passenger';
  }

  return 'Unknown';
}

export function createLobbyForViewer(
  lobby: GameLobby,
  viewerUserId: string,
  isViewerOrganizer: boolean
): GameLobby {
  // Create a copy of the lobby with privacy-filtered slot information
  return {
    ...lobby,
    slots: lobby.slots.map(slot => ({
      ...slot,
      // Keep user data for organizers, organizer slot (always visible), and the user's own slot
      user: (isViewerOrganizer || slot.user?.id === viewerUserId || slot.type === 'organizer')
        ? slot.user
        : slot.user
          ? { ...slot.user, username: 'Anonymous Passenger' }
          : undefined
    }))
  };
}
