import { <PERSON><PERSON><PERSON><PERSON>, Driver, DiscordUser } from '../types/lobby';

export function generateLobbyId(): string {
  return `lobby_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function initializeDrivers(maxDrivers: number): Driver[] {
  const drivers: Driver[] = [];
  for (let i = 1; i <= maxDrivers; i++) {
    drivers.push({
      position: i,
      user: undefined,
      signedUpAt: undefined
    });
  }
  return drivers;
}

export function isLobbyFull(lobby: GameLobby): boolean {
  return lobby.drivers.filter(driver => driver.user).length >= lobby.maxDrivers;
}

export function getAvailableDriverPositions(lobby: GameLobby): number[] {
  return lobby.drivers
    .filter(driver => !driver.user)
    .map(driver => driver.position);
}

export function isUserSignedUp(lobby: GameLobby, userId: string): boolean {
  return lobby.drivers.some(driver => driver.user?.id === userId);
}

export function getUserDriverPosition(lobby: GameLobby, userId: string): number | null {
  const driver = lobby.drivers.find(driver => driver.user?.id === userId);
  return driver ? driver.position : null;
}

export function canUserSignUp(lobby: GameLobby, userId: string, position: number): {
  canSignUp: boolean;
  reason?: string;
} {
  // Check if lobby is open
  if (lobby.status !== 'open') {
    return { canSignUp: false, reason: 'Lobby is not open for signups' };
  }

  // Check if user is already signed up
  if (isUserSignedUp(lobby, userId)) {
    return { canSignUp: false, reason: 'You are already signed up for this lobby' };
  }

  // Check if position is valid
  if (position < 1 || position > lobby.maxDrivers) {
    return { canSignUp: false, reason: 'Invalid driver position' };
  }

  // Check if position is available
  const driver = lobby.drivers.find(d => d.position === position);
  if (!driver || driver.user) {
    return { canSignUp: false, reason: 'Driver position is already taken' };
  }

  // Check if lobby is full
  if (isLobbyFull(lobby)) {
    return { canSignUp: false, reason: 'Lobby is full' };
  }

  return { canSignUp: true };
}

export function validateLobbyData(data: {
  title: string;
  scheduledTime: string;
  maxDrivers: number;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate title
  if (!data.title || data.title.trim().length === 0) {
    errors.push('Title is required');
  } else if (data.title.length > 100) {
    errors.push('Title must be 100 characters or less');
  }

  // Validate scheduled time
  const scheduledTime = new Date(data.scheduledTime);
  if (isNaN(scheduledTime.getTime())) {
    errors.push('Invalid scheduled time');
  } else if (scheduledTime <= new Date()) {
    errors.push('Scheduled time must be in the future');
  }

  // Validate max drivers
  if (!Number.isInteger(data.maxDrivers) || data.maxDrivers < 1 || data.maxDrivers > 7) {
    errors.push('Max drivers must be between 1 and 7');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function formatLobbyTime(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short'
  }).format(date);
}

export function getLobbyStatusColor(status: GameLobby['status']): string {
  switch (status) {
    case 'open':
      return '#4caf50'; // green
    case 'full':
      return '#ff9800'; // orange
    case 'started':
      return '#2196f3'; // blue
    case 'completed':
      return '#9e9e9e'; // gray
    case 'cancelled':
      return '#f44336'; // red
    default:
      return '#9e9e9e';
  }
}

export function updateLobbyStatus(lobby: GameLobby): GameLobby['status'] {
  const now = new Date();
  const scheduledTime = new Date(lobby.scheduledTime);
  
  // If lobby is cancelled, keep it cancelled
  if (lobby.status === 'cancelled') {
    return 'cancelled';
  }
  
  // If lobby is completed, keep it completed
  if (lobby.status === 'completed') {
    return 'completed';
  }
  
  // If scheduled time has passed, mark as started or completed
  if (scheduledTime <= now) {
    return lobby.status === 'started' ? 'started' : 'started';
  }
  
  // If lobby is full, mark as full
  if (isLobbyFull(lobby)) {
    return 'full';
  }
  
  // Otherwise, lobby is open
  return 'open';
}
