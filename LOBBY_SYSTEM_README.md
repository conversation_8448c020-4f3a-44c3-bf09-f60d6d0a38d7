# Game Lobby System

A comprehensive lobby management system for Discord-authenticated users to create and join game lobbies.

## Features

- **Discord Authentication**: Users must be authenticated via Discord to access the lobby system
- **Organizer Permissions**: Only authorized Discord users can create lobbies
- **Driver Positions**: Each lobby supports 1-7 driver positions that users can sign up for
- **Real-time Updates**: Lobby status updates automatically based on signup status and scheduled time
- **Responsive UI**: Works on desktop and mobile devices

## Setup Instructions

### 1. Environment Variables

Make sure you have the following environment variables configured:

```env
# Discord OAuth (already configured)
DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret

# MongoDB (already configured)
MONGODB_URI=your_mongodb_connection_string
MONGODB_DB=your_database_name

# Admin Discord IDs (optional - for organizer management)
ADMIN_DISCORD_IDS=discord_id_1,discord_id_2
```

### 2. Configure Organizers

1. Navigate to `/admin/organizers` (requires admin access)
2. Add Discord user IDs of users who should be able to create lobbies
3. To find Discord IDs:
   - Enable Developer Mode in Discord (User Settings → Advanced → Developer Mode)
   - Right-click on a user and select "Copy ID"

### 3. Database Collections

The system will automatically create these MongoDB collections:

- `game_lobbies`: Stores lobby information and signups
- `organizer_config`: Stores authorized organizer Discord IDs

## Usage

### For Regular Users

1. Sign in with Discord
2. Navigate to "Game Lobbies" in the navigation
3. Browse available lobbies
4. Click "Join as Driver X" to sign up for a position
5. View your joined lobbies in the "Joined Lobbies" tab

### For Organizers

1. Sign in with Discord (must be in organizer list)
2. Navigate to "Game Lobbies"
3. Click the "+" button to create a new lobby
4. Fill in lobby details:
   - Title and description
   - Scheduled time
   - Number of driver positions (1-7)
5. Manage your lobbies in the "My Lobbies" tab

## API Endpoints

### Lobbies
- `GET /api/lobbies` - Get all lobbies (with optional filters)
- `POST /api/lobbies` - Create a new lobby (organizers only)
- `GET /api/lobbies/[lobbyId]` - Get specific lobby
- `PUT /api/lobbies/[lobbyId]` - Update lobby (organizer only)
- `DELETE /api/lobbies/[lobbyId]` - Delete lobby (organizer only)

### Signups
- `POST /api/lobbies/[lobbyId]/signup` - Sign up for a lobby
- `DELETE /api/lobbies/[lobbyId]/signup` - Remove signup

### Organizers
- `GET /api/organizers` - Get organizer configuration
- `POST /api/organizers` - Update organizer configuration (admin only)

## File Structure

```
src/
├── components/lobby/
│   ├── CreateLobbyForm.tsx    # Form for creating new lobbies
│   └── LobbyCard.tsx          # Display component for individual lobbies
├── hooks/
│   └── useLobby.ts            # React hook for lobby operations
├── services/
│   └── lobbyService.ts        # MongoDB service for lobby operations
├── types/
│   └── lobby.ts               # TypeScript interfaces
└── utils/
    └── lobby.ts               # Utility functions

pages/
├── api/lobbies/               # API endpoints
├── admin/organizers.tsx       # Organizer management page
└── lobbies.tsx                # Main lobby interface
```

## Lobby States

- **open**: Accepting signups
- **full**: All driver positions filled
- **started**: Scheduled time has passed
- **completed**: Manually marked as completed
- **cancelled**: Manually cancelled

## Security Features

- Discord authentication required for all operations
- Organizer authorization for lobby creation
- User can only sign up for one position per lobby
- Organizers can only modify their own lobbies
- Admin-only organizer configuration

## Troubleshooting

### Users can't create lobbies
- Check if their Discord ID is in the organizer configuration
- Verify they're properly authenticated with Discord

### Lobbies not loading
- Check MongoDB connection
- Verify API endpoints are accessible
- Check browser console for errors

### Discord authentication issues
- Verify Discord OAuth credentials
- Check Discord application settings
- Ensure redirect URLs are configured correctly

## Future Enhancements

- Email/Discord notifications for lobby updates
- Lobby templates for recurring events
- Advanced filtering and search
- Integration with calendar systems
- Lobby chat/messaging system
