import { useState, useEffect } from 'react';
import Head from 'next/head';
import type { ReactElement } from 'react';
import {
  Container,
  Grid,
  Card,
  CardHeader,
  CardContent,
  Divider,
  Typography,
  Box,
  Button,
  CircularProgress
} from '@mui/material';
import NotificationSettings from 'src/components/NotificationSettings';
import { useSession, signIn } from "next-auth/react";
import Header from 'src/components/Header';
import { styled } from '@mui/material/styles';

// Extend the Session type to include the user ID
declare module "next-auth" {
  interface Session {
    user?: {
      name?: string | null;
      email?: string | null;
      image?: string | null;
      id?: string | null;
    };
  }
}

const NotificationsWrapper = styled(Box)(
  ({ theme }) => `
    overflow: auto;
    background: ${theme.palette.common.white};
    flex: 1;
    overflow-x: hidden;
`
);

function NotificationsPage() {
  const { data: session, status } = useSession();
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    setLoading(true);
    await signIn('discord', { callbackUrl: '/notifications' });
  };

  return (
    <>
      <Head>
        <title>Discord Notifications - Lost Ark Pals</title>
      </Head>
      <NotificationsWrapper>
        <Header />
        <Container maxWidth="lg" sx={{ mt: 3, mb: 6 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Card>
                <CardHeader title="Discord Notifications" />
                <Divider />
                <CardContent>
                  {status === 'loading' ? (
                    <Box textAlign="center" py={3}>
                      <CircularProgress />
                      <Typography variant="body1" sx={{ mt: 2 }}>
                        Loading authentication status...
                      </Typography>
                    </Box>
                  ) : session ? (
                    <Box>
                      <Typography variant="body1" paragraph>
                        Stay updated with the latest Discord messages from Lost Ark Pals. 
                        Enable notifications to receive alerts when new messages are posted.
                      </Typography>
                      <NotificationSettings userId={session.user?.id} />
                    </Box>
                  ) : (
                    <Box textAlign="center" py={3}>
                      <Typography variant="h6" gutterBottom>
                        Please log in to manage notification settings
                      </Typography>
                      <Button 
                        variant="contained" 
                        color="primary" 
                        onClick={handleLogin}
                        disabled={loading}
                        sx={{ mt: 2 }}
                      >
                        {loading ? (
                          <>
                            <CircularProgress size={24} sx={{ mr: 1, color: 'white' }} />
                            Logging in...
                          </>
                        ) : (
                          'Login with Discord'
                        )}
                      </Button>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </NotificationsWrapper>
    </>
  );
}

export default NotificationsPage;

