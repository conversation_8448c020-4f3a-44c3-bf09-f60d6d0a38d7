import {
  Typography,
  Box,
  Container,
  styled,
  Grid,
  Paper
} from '@mui/material';
import type { ReactElement } from 'react';
import BaseLayout from 'src/layouts/BaseLayout';
import Link from 'src/components/Link';
import Head from 'next/head';
import Hero from 'src/content/Overview/Hero';
import Header from 'src/components/Header';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import styles from '../styles/Home.module.css';
import { DiscordMessage } from 'src/types/discord';
import DiscordMessageComponent from '../components/DiscordMessage';
import { formatDistanceToNow } from 'date-fns';
import NotificationSettings from 'src/components/NotificationSettings';

const OverviewWrapper = styled(Box)(
  ({ theme }) => `
    overflow: auto;
    background: ${theme.palette.common.white};
    flex: 1;
    overflow-x: hidden;
`
);

const DiscordFeedWrapper = styled(Paper)(
  ({ theme }) => `
    padding: ${theme.spacing(3)};
    border-radius: ${theme.shape.borderRadius}px;
    background: ${theme.palette.background.paper};
    height: 100%;
    max-height: 600px;
    overflow-y: auto;
`
);

function Overview() {
  const [discordMessages, setDiscordMessages] = useState<DiscordMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeMessage, setActiveMessage] = useState(0);

  useEffect(() => {
    // Fetch Discord messages
    const fetchDiscordMessages = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/discord-messages');
        const data = await response.json();
        
        if (data.messages && data.messages.length > 0) {
          // Filter messages to only include those from the last hour
          const oneHourAgo = Date.now() - 60 * 60 * 1000;
          const recentMessages = data.messages.filter(
            (msg: DiscordMessage) => msg.timestamp > oneHourAgo
          );
          
          setDiscordMessages(recentMessages);
          setActiveMessage(0); // Reset to first message when new data arrives
        }
      } catch (error) {
        console.error('Error fetching Discord messages:', error);
      } finally {
        setLoading(false);
      }
    };

    // Initial fetch
    fetchDiscordMessages();

    // Set up interval to fetch messages
    const interval = setInterval(fetchDiscordMessages, 60000); // 1 minute

    // Auto-rotate through messages every 8 seconds if there are multiple
    const rotationInterval = setInterval(() => {
      if (discordMessages.length > 1) {
        setActiveMessage(prev => (prev + 1) % discordMessages.length);
      }
    }, 8000);

    // Clean up intervals
    return () => {
      clearInterval(interval);
      clearInterval(rotationInterval);
    };
  }, [discordMessages.length]);

  // Navigate to previous message
  const prevMessage = () => {
    if (discordMessages.length > 1) {
      setActiveMessage(prev => 
        prev === 0 ? discordMessages.length - 1 : prev - 1
      );
    }
  };

  // Navigate to next message
  const nextMessage = () => {
    if (discordMessages.length > 1) {
      setActiveMessage(prev => 
        (prev + 1) % discordMessages.length
      );
    }
  };

  // Format relative time
  const formatRelativeTime = (timestamp: number) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  return (
    <OverviewWrapper>
      <Head>
        <title>Lost Ark Pals - Buy Lost Ark Busses</title>
      </Head>
      <Header />
      <Hero />

      {/* Main Content Section - Split Layout */}
      <Container maxWidth="lg" sx={{ mt: 6, mb: 6 }}>
        <Grid container spacing={4}>
          {/* Left Side - Original Content */}
          <Grid item xs={12} md={6}>
            <Typography variant="h3" gutterBottom>
              Buy Lost Ark Busses
            </Typography>
            <Typography paragraph>
              Lost Ark Pals provides fast and efficient busses for Lost Ark raids in NA East region.
              Selling Mordum Hardmode Title "Lord of Thunder". Join our discord for requests! We have over 20+ runs already done.
              Our bussing community over 200+ bus runs completed every month, with over 
              500 monthly active users.
            </Typography>
            <Box sx={{ mt: 4 }}>
              <Typography variant="h4" gutterBottom>
                Our Services
              </Typography>
              <ul>
                <li>Mordum Normal/Hard Busses</li>
                <li>Brelshaza v2 Normal/Hard Busses</li>
                <li>Aegir Normal/Hard Busses</li>
                <li>Behemoth Busses</li>
                <li>Echidna Hard Busses</li>
                <li>Thaemine Hard Busses</li>
              </ul>
            </Box>
          </Grid>

          {/* Right Side - Discord Live Feed */}
          <Grid item xs={12} md={6}>
            <Typography variant="h3" gutterBottom>
              Live Discord Updates
            </Typography>

            <div className={styles.discordCard}>
              {loading ? (
                <div className={styles.loading}>
                  <div className={styles.loadingSpinner}></div>
                  <Typography>Loading latest updates...</Typography>
                </div>
              ) : discordMessages.length > 0 ? (
                <>
                  {/* Current Message Display */}
                  <div className={styles.discordMessageWrapper}>
                    <div className={styles.discordHeader}>
                      <div className={styles.discordAuthor}>
                        {discordMessages[activeMessage].author.avatarUrl && (
                          <Image 
                            src={discordMessages[activeMessage].author.avatarUrl} 
                            alt={discordMessages[activeMessage].author.username}
                            width={40}
                            height={40}
                            className={styles.authorAvatar}
                          />
                        )}
                        <Typography 
                          variant="subtitle1" 
                          sx={{ 
                            color: '#f00e0e', 
                            fontWeight: '',
                            marginLeft: '12px' 
                          }}
                        >
                          {discordMessages[activeMessage].author.username}
                        </Typography>
                      </div>
                      <div className={styles.discordMeta}>
                        <Typography variant="caption" color="text.secondary">
                          #{discordMessages[activeMessage].channelName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDistanceToNow(new Date(discordMessages[activeMessage].timestamp), { addSuffix: true })}
                        </Typography>
                      </div>
                    </div>
                    
                    {/* Message Content */}
                    <Box sx={{ my: 2, flex: 1 }}>
                      <DiscordMessageComponent message={discordMessages[activeMessage]} />
                    </Box>
                    
                    {/* Navigation Controls */}
                    {discordMessages.length > 1 && (
                      <div className={styles.discordNavigation}>
                        <button onClick={prevMessage} className={styles.navButton}>
                          &lt; Prev
                        </button>
                        <div className={styles.navDots}>
                          {discordMessages.map((_, index) => (
                            <span 
                              key={index} 
                              className={`${styles.navDot} ${index === activeMessage ? styles.activeDot : ''}`}
                              onClick={() => setActiveMessage(index)}
                            />
                          ))}
                        </div>
                        <button onClick={nextMessage} className={styles.navButton}>
                          Next &gt;
                        </button>
                      </div>
                    )}
                  </div>
                </>
              ) : (
                <div className={styles.noMessage}>
                  <Typography>No Discord messages from the last hour</Typography>
                </div>
              )}
            </div>
          </Grid>

          {/* Notification Settings */}
          <Grid item xs={12}>
            <NotificationSettings />
          </Grid>
        </Grid>
      </Container>

      <Container maxWidth="lg" sx={{ mt: 8 }}>
        <Typography textAlign="center" variant="subtitle1">
          Powered by{' '}
          <Link
            href="https://tokyo.bloomui.com/" 
            target="_blank"
            rel="noopener noreferrer"
          >
            Tokyo Admin Dashboard
          </Link>
          {' '}| © 2025 Lost Ark Pals{' '}
        </Typography>
      </Container>
    </OverviewWrapper>
  );
}

export default Overview;

Overview.getLayout = function getLayout(page: ReactElement) {
  return <BaseLayout>{page}</BaseLayout>;
};
