import { useEffect, useRef, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { GameLobby } from '../types/lobby';

export interface UseLobbyPollingOptions {
  onLobbiesChanged?: (lobbies: GameLobby[]) => void;
  pollingInterval?: number; // in milliseconds
  enabled?: boolean;
}

export function useLobbyPolling(options: UseLobbyPollingOptions = {}) {
  const { data: session } = useSession();
  const {
    onLobbiesChanged,
    pollingInterval = 2000, // Poll every 2 seconds
    enabled = true
  } = options;

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastDataRef = useRef<string>('');
  const isPollingRef = useRef(false);

  const fetchLobbies = useCallback(async () => {
    if (!session?.user || isPollingRef.current) {
      return;
    }

    try {
      isPollingRef.current = true;
      const response = await fetch('/api/lobbies');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.lobbies) {
        // Compare with last data to detect changes
        const currentDataString = JSON.stringify(data.lobbies);
        if (currentDataString !== lastDataRef.current) {
          lastDataRef.current = currentDataString;
          onLobbiesChanged?.(data.lobbies);
        }
      }
    } catch (error) {
      console.error('Error polling lobbies:', error);
    } finally {
      isPollingRef.current = false;
    }
  }, [session, onLobbiesChanged]);

  const startPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    if (enabled && session?.user) {
      // Fetch immediately
      fetchLobbies();
      
      // Then start polling
      intervalRef.current = setInterval(fetchLobbies, pollingInterval);
      console.log(`Started lobby polling every ${pollingInterval}ms`);
    }
  }, [enabled, session, fetchLobbies, pollingInterval]);

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
      console.log('Stopped lobby polling');
    }
  }, []);

  // Start/stop polling based on session and enabled state
  useEffect(() => {
    if (enabled && session?.user) {
      startPolling();
    } else {
      stopPolling();
    }

    return () => {
      stopPolling();
    };
  }, [enabled, session?.user?.id, startPolling, stopPolling]);

  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && enabled && session?.user) {
        startPolling();
      } else {
        stopPolling();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [enabled, session, startPolling, stopPolling]);

  return {
    startPolling,
    stopPolling,
    isPolling: intervalRef.current !== null
  };
}
