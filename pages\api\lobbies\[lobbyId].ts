import type { NextApiRequest, NextApiResponse } from 'next/types';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { LobbyService } from '../../../src/services/lobbyService';
import { LobbyResponse } from '../../../src/types/lobby';

const lobbyService = new LobbyService();

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  const { lobbyId } = req.query;

  if (!lobbyId || typeof lobbyId !== 'string') {
    return res.status(400).json({ 
      success: false, 
      error: 'Invalid lobby ID' 
    });
  }

  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session || !session.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const discordUser = {
      id: (session.user as any).id || session.user.email?.split('@')[0] || 'unknown',
      username: session.user.name || 'Unknown User',
      discriminator: '0000',
      avatar: session.user.image || undefined,
      email: session.user.email || undefined
    };

    if (req.method === 'GET') {
      // Get specific lobby
      const lobby = await lobbyService.getLobbyById(lobbyId);
      
      if (!lobby) {
        const response: LobbyResponse = {
          success: false,
          error: 'Lobby not found'
        };
        return res.status(404).json(response);
      }

      const response: LobbyResponse = {
        success: true,
        lobby
      };

      return res.status(200).json(response);
    }

    if (req.method === 'PUT') {
      // Update lobby (only organizer can update)
      const lobby = await lobbyService.getLobbyById(lobbyId);
      
      if (!lobby) {
        return res.status(404).json({ 
          success: false, 
          error: 'Lobby not found' 
        });
      }

      // Check if user is the organizer
      if (lobby.organizerId !== discordUser.id) {
        return res.status(403).json({ 
          success: false, 
          error: 'Only the organizer can update this lobby' 
        });
      }

      const { title, description, scheduledTime, status } = req.body;

      // Update allowed fields
      if (title !== undefined) lobby.title = title;
      if (description !== undefined) lobby.description = description;
      if (scheduledTime !== undefined) lobby.scheduledTime = new Date(scheduledTime);
      if (status !== undefined) lobby.status = status;

      try {
        const updatedLobby = await lobbyService.updateLobby(lobby);
        
        const response: LobbyResponse = {
          success: true,
          lobby: updatedLobby
        };

        return res.status(200).json(response);
      } catch (error) {
        console.error('Error updating lobby:', error);
        return res.status(500).json({ 
          success: false, 
          error: 'Failed to update lobby' 
        });
      }
    }

    if (req.method === 'DELETE') {
      // Delete lobby (only organizer can delete)
      const lobby = await lobbyService.getLobbyById(lobbyId);
      
      if (!lobby) {
        return res.status(404).json({ 
          success: false, 
          error: 'Lobby not found' 
        });
      }

      // Check if user is the organizer
      if (lobby.organizerId !== discordUser.id) {
        return res.status(403).json({ 
          success: false, 
          error: 'Only the organizer can delete this lobby' 
        });
      }

      try {
        const deleted = await lobbyService.deleteLobby(lobbyId);
        
        if (deleted) {
          return res.status(200).json({ 
            success: true, 
            message: 'Lobby deleted successfully' 
          });
        } else {
          return res.status(500).json({ 
            success: false, 
            error: 'Failed to delete lobby' 
          });
        }
      } catch (error) {
        console.error('Error deleting lobby:', error);
        return res.status(500).json({ 
          success: false, 
          error: 'Failed to delete lobby' 
        });
      }
    }

    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });

  } catch (error) {
    console.error('Error in lobby API:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
}
