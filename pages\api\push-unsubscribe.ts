import type { NextApiRequest, NextApiResponse } from 'next/types';
import { connectToDatabase } from '../../lib/mongodb';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get the subscription object from the request
    const subscription = req.body;
    
    if (!subscription || !subscription.endpoint) {
      return res.status(400).json({ error: 'Invalid subscription data' });
    }

    try {
      // Connect to the database
      const { db } = await connectToDatabase();
      
      // Remove the subscription from the database
      await db.collection('push_subscriptions').deleteOne({
        'subscription.endpoint': subscription.endpoint
      });
    } catch (dbError) {
      console.error('Database error when removing subscription:', dbError);
      // Still return success to client, but log the error
    }

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error removing push subscription:', error);
    return res.status(500).json({ error: 'Failed to remove subscription' });
  }
}

