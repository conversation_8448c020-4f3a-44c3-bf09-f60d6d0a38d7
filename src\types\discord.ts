export interface DiscordAuthor {
  username: string;
  avatarUrl: string;
}

export interface RoleMention {
  id: string;
  name: string;
  color: string;
}

export interface DiscordMessage {
  id: string;
  content: string;
  author: DiscordAuthor;
  timestamp: number;
  imageUrl: string;
  channelName: string;
  channelId: string;
  roleMentions?: RoleMention[]; // Add the roleMentions property as optional
}
