import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box
} from '@mui/material';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import pushNotificationService from '../../services/pushNotificationService';

const NotificationPrompt: React.FC = () => {
  const [open, setOpen] = useState(false);

  useEffect(() => {
    // Check if this is the first visit or if we've already asked
    const hasPrompted = localStorage.getItem('notificationPrompted');
    
    if (!hasPrompted && pushNotificationService.isSupported()) {
      // Only show after a short delay to not interrupt initial page load
      const timer = setTimeout(() => {
        if (Notification.permission === 'default') {
          setOpen(true);
        }
        // Mark that we've prompted the user
        localStorage.setItem('notificationPrompted', 'true');
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, []);

  const handleEnableNotifications = async () => {
    const granted = await pushNotificationService.requestPermission();
    
    if (granted) {
      const registration = await pushNotificationService.registerServiceWorker();
      if (registration) {
        const subscription = await pushNotificationService.subscribe(registration);
        if (subscription) {
          pushNotificationService.saveSettings({ enabled: true });
        }
      }
    }
    
    setOpen(false);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      aria-labelledby="notification-prompt-title"
    >
      <DialogTitle id="notification-prompt-title">
        <Box display="flex" alignItems="center">
          <NotificationsActiveIcon color="primary" sx={{ mr: 1 }} />
          Stay Updated with Discord
        </Box>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body1" paragraph>
          Would you like to receive notifications when new Discord messages are posted?
        </Typography>
        <Typography variant="body2" color="text.secondary">
          You can change this setting anytime in your user preferences.
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} color="inherit">
          Not Now
        </Button>
        <Button onClick={handleEnableNotifications} variant="contained" color="primary">
          Enable Notifications
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default NotificationPrompt;
