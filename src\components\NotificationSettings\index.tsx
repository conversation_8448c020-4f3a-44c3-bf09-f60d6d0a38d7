import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Switch, 
  FormControlLabel, 
  Paper, 
  Button,
  Alert,
  Snackbar
} from '@mui/material';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import NotificationsOffIcon from '@mui/icons-material/NotificationsOff';
import pushNotificationService from '../../services/pushNotificationService';

interface NotificationSettingsProps {
  userId?: string | null;
}

const NotificationSettings: React.FC<NotificationSettingsProps> = ({ userId }) => {
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<NotificationPermission>('default');
  const [isSupported, setIsSupported] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertSeverity, setAlertSeverity] = useState<'success' | 'error' | 'info'>('info');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Check if notifications are supported
    const supported = pushNotificationService.isSupported();
    setIsSupported(supported);
    
    if (supported) {
      // Get current permission status
      setPermissionStatus(Notification.permission);
      
      // Get saved settings
      const settings = pushNotificationService.getSettings();
      setNotificationsEnabled(settings.enabled);
    }
  }, []);

  const handleToggleNotifications = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const enabled = event.target.checked;
    setIsLoading(true);
    
    try {
      if (enabled) {
        // Request permission if not already granted
        if (permissionStatus !== 'granted') {
          const granted = await pushNotificationService.requestPermission();
          setPermissionStatus(granted ? 'granted' : 'denied');
          
          if (!granted) {
            setAlertMessage('Notification permission denied. Please enable notifications in your browser settings.');
            setAlertSeverity('error');
            setShowAlert(true);
            setIsLoading(false);
            return;
          }
        }
        
        // Unregister any existing service worker to ensure a fresh start
        if ('serviceWorker' in navigator) {
          const registrations = await navigator.serviceWorker.getRegistrations();
          for (const registration of registrations) {
            await registration.unregister();
          }
          console.log('Unregistered existing service workers');
        }
        
        // Register service worker and subscribe
        const registration = await pushNotificationService.registerServiceWorker();
        if (registration) {
          console.log('Service worker registered successfully');
          
          // Wait for the service worker to be activated
          if (registration.active) {
            console.log('Service worker is already active');
          } else {
            console.log('Waiting for service worker to activate...');
            await new Promise<void>((resolve) => {
              registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                if (newWorker) {
                  newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'activated') {
                      console.log('Service worker activated');
                      resolve();
                    }
                  });
                }
              });
              
              // Fallback timeout
              setTimeout(() => {
                console.log('Service worker activation timeout - continuing anyway');
                resolve();
              }, 3000);
            });
          }
          
          const subscription = await pushNotificationService.subscribe(registration);
          if (subscription) {
            console.log('Successfully subscribed to push notifications');
            setNotificationsEnabled(true);
            pushNotificationService.saveSettings({ enabled: true });
            
            // Send subscription to server
            const response = await fetch('/api/push-subscribe', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ subscription }),
            });
            
            if (response.ok) {
              setAlertMessage('Discord notifications enabled successfully!');
              setAlertSeverity('success');
              
              // Test notification
              try {
                const testResponse = await fetch('/api/test-notification', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({ subscription }),
                });
                
                if (testResponse.ok) {
                  console.log('Test notification sent successfully');
                } else {
                  console.error('Failed to send test notification');
                }
              } catch (error) {
                console.error('Error sending test notification:', error);
              }
            } else {
              setAlertMessage('Failed to save notification settings on server.');
              setAlertSeverity('error');
            }
            setShowAlert(true);
          } else {
            setAlertMessage('Failed to subscribe to notifications.');
            setAlertSeverity('error');
            setShowAlert(true);
          }
        }
      } else {
        // Unsubscribe from notifications
        const unsubscribed = await pushNotificationService.unsubscribe();
        if (unsubscribed) {
          setNotificationsEnabled(false);
          pushNotificationService.saveSettings({ enabled: false });
          
          // Notify server about unsubscription
          const currentSubscription = await pushNotificationService.getSubscription();
          if (currentSubscription) {
            await fetch('/api/push-unsubscribe', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ subscription: currentSubscription }),
            });
          }
          
          setAlertMessage('Discord notifications disabled.');
          setAlertSeverity('info');
          setShowAlert(true);
        }
      }
    } catch (error) {
      console.error('Error toggling notifications:', error);
      setAlertMessage('An error occurred while updating notification settings.');
      setAlertSeverity('error');
      setShowAlert(true);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isSupported) {
    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Discord Notifications
        </Typography>
        <Alert severity="warning">
          Push notifications are not supported in your browser.
        </Alert>
      </Paper>
    );
  }

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Box display="flex" alignItems="center" mb={2}>
        {notificationsEnabled ? 
          <NotificationsActiveIcon color="primary" sx={{ mr: 1 }} /> : 
          <NotificationsOffIcon sx={{ mr: 1 }} />
        }
        <Typography variant="h5">
          Discord Notifications
        </Typography>
      </Box>
      
      <Typography variant="body1" paragraph>
        Receive notifications when new Discord messages are posted.
      </Typography>
      
      <FormControlLabel
        control={
          <Switch
            checked={notificationsEnabled}
            onChange={handleToggleNotifications}
            color="primary"
          />
        }
        label="Enable Discord notifications"
      />
      
      {permissionStatus === 'denied' && (
        <Alert severity="warning" sx={{ mt: 2 }}>
          Notifications are blocked. Please update your browser settings to enable notifications.
          <Button 
            size="small" 
            sx={{ ml: 1 }}
            onClick={() => window.open('chrome://settings/content/notifications')}
          >
            Open Settings
          </Button>
        </Alert>
      )}
      
      <Snackbar
        open={showAlert}
        autoHideDuration={6000}
        onClose={() => setShowAlert(false)}
      >
        <Alert 
          onClose={() => setShowAlert(false)} 
          severity={alertSeverity}
          sx={{ width: '100%' }}
        >
          {alertMessage}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default NotificationSettings;




