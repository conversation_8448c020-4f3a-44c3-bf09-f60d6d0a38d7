import type { NextApiRequest, NextApiResponse } from 'next/types';
import { connectToDatabase } from '../../lib/mongodb';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { subscription } = req.body;
    
    if (!subscription) {
      return res.status(400).json({ error: 'No subscription provided' });
    }

    console.log('Received push subscription:', subscription.endpoint);

    try {
      // Connect to the database
      const { db } = await connectToDatabase();
      
      // Store the subscription in the database
      await db.collection('push_subscriptions').updateOne(
        { 'subscription.endpoint': subscription.endpoint },
        { 
          $set: { 
            subscription, 
            updatedAt: new Date() 
          },
          $setOnInsert: { 
            createdAt: new Date() 
          }
        },
        { upsert: true }
      );
      
      console.log('Subscription saved successfully');
      
      return res.status(200).json({ 
        success: true,
        message: 'Subscription saved successfully'
      });
    } catch (dbError) {
      console.error('Database error when saving subscription:', dbError);
      
      // Check for specific MongoDB errors
      const mongoError = dbError as Error;
      const errorMessage = mongoError.message || 'Unknown database error';
      
      // Still return success to client, but log the error
      // This allows the app to work even if MongoDB is temporarily unavailable
      return res.status(200).json({ 
        success: true,
        warning: 'Subscription saved locally only due to database error',
        errorDetails: errorMessage
      });
    }
  } catch (error) {
    console.error('Error processing subscription:', error);
    return res.status(500).json({ 
      error: 'Failed to process subscription',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}



