import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    console.log('Testing session endpoint...');
    
    const session = await getServerSession(req, res, authOptions);
    
    console.log('Session result:', session);
    
    return res.status(200).json({
      success: true,
      session,
      hasSession: !!session,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Session test error:', error);
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
