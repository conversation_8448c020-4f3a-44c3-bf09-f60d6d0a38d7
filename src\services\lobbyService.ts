import { Db, Collection } from 'mongodb';
import { connectToDatabase } from '../../lib/mongodb';
import { GameLobby, DiscordUser, OrganizerConfig, LobbySignup } from '../types/lobby';
import { generateLobbyId, initializeRaidSlots, updateLobbyStatus } from '../utils/lobby';
import { broadcastLobbyEvent } from '../utils/eventBroadcaster';

export class LobbyService {
  private db: Db | null = null;
  private lobbiesCollection: Collection<GameLobby> | null = null;
  private organizersCollection: Collection<OrganizerConfig> | null = null;

  private async ensureConnection(): Promise<void> {
    if (!this.db) {
      const connection = await connectToDatabase();
      this.db = connection.db;
      this.lobbiesCollection = this.db.collection<GameLobby>('game_lobbies');
      this.organizersCollection = this.db.collection<OrganizerConfig>('organizer_config');
    }
  }

  async createLobby(
    title: string,
    description: string | undefined,
    scheduledTime: Date,
    maxPassengers: number,
    organizer: DiscordUser
  ): Promise<GameLobby> {
    await this.ensureConnection();

    const lobby: GameLobby = {
      id: generateLobbyId(),
      title,
      description,
      organizerId: organizer.id,
      organizer,
      scheduledTime,
      maxPassengers,
      slots: initializeRaidSlots(maxPassengers, organizer),
      status: 'open',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await this.lobbiesCollection!.insertOne(lobby);
    lobby._id = result.insertedId.toString();

    // Broadcast lobby created event
    console.log('Broadcasting lobby_created event for lobby:', lobby.id);
    broadcastLobbyEvent({
      type: 'lobby_created',
      lobbyId: lobby.id,
      data: lobby,
      timestamp: new Date().toISOString()
    });

    return lobby;
  }

  async getLobbyById(lobbyId: string): Promise<GameLobby | null> {
    await this.ensureConnection();
    
    const lobby = await this.lobbiesCollection!.findOne({ id: lobbyId });
    if (lobby) {
      lobby.status = updateLobbyStatus(lobby);
      await this.updateLobby(lobby);
    }
    return lobby;
  }

  async getLobbies(
    status?: GameLobby['status'],
    organizerId?: string,
    limit: number = 50
  ): Promise<GameLobby[]> {
    await this.ensureConnection();

    const filter: any = {};
    if (status) filter.status = status;
    if (organizerId) filter.organizerId = organizerId;

    const lobbies = await this.lobbiesCollection!
      .find(filter)
      .sort({ scheduledTime: 1 })
      .limit(limit)
      .toArray();

    // Update statuses for all lobbies
    const updatedLobbies = lobbies.map(lobby => {
      const newStatus = updateLobbyStatus(lobby);
      if (newStatus !== lobby.status) {
        lobby.status = newStatus;
        lobby.updatedAt = new Date();
        // Update in background
        this.updateLobby(lobby).catch(console.error);
      }
      return lobby;
    });

    return updatedLobbies;
  }

  async updateLobby(lobby: GameLobby): Promise<GameLobby> {
    await this.ensureConnection();

    lobby.updatedAt = new Date();
    await this.lobbiesCollection!.updateOne(
      { id: lobby.id },
      { $set: lobby }
    );

    return lobby;
  }

  async deleteLobby(lobbyId: string): Promise<boolean> {
    await this.ensureConnection();

    const result = await this.lobbiesCollection!.deleteOne({ id: lobbyId });
    const deleted = result.deletedCount > 0;

    // Broadcast lobby deleted event
    if (deleted) {
      broadcastLobbyEvent({
        type: 'lobby_deleted',
        lobbyId: lobbyId,
        data: { lobbyId },
        timestamp: new Date().toISOString()
      });
    }

    return deleted;
  }

  async signUpForLobby(
    lobbyId: string,
    user: DiscordUser,
    slotPosition: number
  ): Promise<{ success: boolean; lobby?: GameLobby; error?: string }> {
    await this.ensureConnection();

    const lobby = await this.getLobbyById(lobbyId);
    if (!lobby) {
      return { success: false, error: 'Lobby not found' };
    }

    // Find the slot position
    const slotIndex = lobby.slots.findIndex(s => s.position === slotPosition);
    if (slotIndex === -1) {
      return { success: false, error: 'Invalid slot position' };
    }

    const slot = lobby.slots[slotIndex];

    // Check if it's a passenger slot
    if (slot.type !== 'passenger') {
      return { success: false, error: 'You can only sign up for passenger slots' };
    }

    // Check if position is already taken
    if (slot.user) {
      return { success: false, error: 'Passenger slot is already taken' };
    }

    // Check if user is already signed up
    const existingSignup = lobby.slots.find(s => s.user?.id === user.id);
    if (existingSignup) {
      return { success: false, error: 'You are already signed up for this lobby' };
    }

    // Sign up the user
    lobby.slots[slotIndex] = {
      position: slotPosition,
      type: 'passenger',
      user,
      signedUpAt: new Date()
    };

    // Update lobby status
    lobby.status = updateLobbyStatus(lobby);

    const updatedLobby = await this.updateLobby(lobby);

    // Broadcast user joined event
    console.log('Broadcasting user_joined event for lobby:', lobby.id, 'user:', user.username);
    broadcastLobbyEvent({
      type: 'user_joined',
      lobbyId: lobby.id,
      data: { user, slotPosition, lobby: updatedLobby },
      timestamp: new Date().toISOString()
    });

    return { success: true, lobby: updatedLobby };
  }

  async removeSignup(
    lobbyId: string,
    userId: string
  ): Promise<{ success: boolean; lobby?: GameLobby; error?: string }> {
    await this.ensureConnection();

    const lobby = await this.getLobbyById(lobbyId);
    if (!lobby) {
      return { success: false, error: 'Lobby not found' };
    }

    // Find the user's signup
    const slotIndex = lobby.slots.findIndex(s => s.user?.id === userId);
    if (slotIndex === -1) {
      return { success: false, error: 'You are not signed up for this lobby' };
    }

    const slot = lobby.slots[slotIndex];

    // Only allow removing passenger signups (not organizer)
    if (slot.type !== 'passenger') {
      return { success: false, error: 'Cannot remove organizer from lobby' };
    }

    // Remove the signup
    lobby.slots[slotIndex] = {
      position: slot.position,
      type: 'passenger',
      user: undefined,
      signedUpAt: undefined
    };

    // Update lobby status
    lobby.status = updateLobbyStatus(lobby);

    const updatedLobby = await this.updateLobby(lobby);

    // Broadcast user left event
    broadcastLobbyEvent({
      type: 'user_left',
      lobbyId: lobby.id,
      data: { userId, lobby: updatedLobby },
      timestamp: new Date().toISOString()
    });

    return { success: true, lobby: updatedLobby };
  }

  async kickUserFromLobby(
    lobbyId: string,
    userIdToKick: string,
    organizerId: string
  ): Promise<{ success: boolean; lobby?: GameLobby; error?: string }> {
    await this.ensureConnection();

    const lobby = await this.getLobbyById(lobbyId);
    if (!lobby) {
      return { success: false, error: 'Lobby not found' };
    }

    // Check if the requester is the organizer
    if (lobby.organizerId !== organizerId) {
      return { success: false, error: 'Only the organizer can kick users from this lobby' };
    }

    // Find the user's signup
    const slotIndex = lobby.slots.findIndex(s => s.user?.id === userIdToKick);
    if (slotIndex === -1) {
      return { success: false, error: 'User is not signed up for this lobby' };
    }

    const slot = lobby.slots[slotIndex];

    // Only allow kicking passengers (not organizer)
    if (slot.type !== 'passenger') {
      return { success: false, error: 'Cannot kick the organizer from lobby' };
    }

    // Remove the user
    lobby.slots[slotIndex] = {
      position: slot.position,
      type: 'passenger',
      user: undefined,
      signedUpAt: undefined
    };

    // Update lobby status
    lobby.status = updateLobbyStatus(lobby);

    const updatedLobby = await this.updateLobby(lobby);

    // Broadcast user kicked event
    broadcastLobbyEvent({
      type: 'user_kicked',
      lobbyId: lobby.id,
      data: { kickedUserId: userIdToKick, organizerId, lobby: updatedLobby },
      timestamp: new Date().toISOString()
    });

    return { success: true, lobby: updatedLobby };
  }

  async isUserOrganizer(userId: string): Promise<boolean> {
    await this.ensureConnection();

    const config = await this.organizersCollection!.findOne({});
    if (!config) {
      return false;
    }

    return config.discordIds.includes(userId);
  }

  async getOrganizerConfig(): Promise<OrganizerConfig | null> {
    await this.ensureConnection();
    return await this.organizersCollection!.findOne({});
  }

  async updateOrganizerConfig(discordIds: string[]): Promise<OrganizerConfig> {
    await this.ensureConnection();

    const config: OrganizerConfig = {
      discordIds,
      lastUpdated: new Date()
    };

    await this.organizersCollection!.replaceOne(
      {},
      config,
      { upsert: true }
    );

    return config;
  }
}
