import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { addConnection, removeConnection, broadcastLobbyEvent, getConnectionCount } from '../../../src/utils/eventBroadcaster';

// Re-export for backward compatibility
export { broadcastLobbyEvent };

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Set up SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
      'X-Accel-Buffering': 'no', // Disable nginx buffering
    });

    // Send initial connection message
    res.write(`data: ${JSON.stringify({
      type: 'connected',
      message: 'Connected to lobby events',
      timestamp: new Date().toISOString()
    })}\n\n`);

    // Add this connection to the broadcaster
    addConnection(res);

    // Prevent the response from timing out
    res.socket?.setTimeout(0);

    // Handle client disconnect
    req.on('close', () => {
      console.log('SSE client disconnected (close event)');
      removeConnection(res);
    });

    req.on('end', () => {
      console.log('SSE client disconnected (end event)');
      removeConnection(res);
    });

    // Handle connection errors
    res.on('error', (error) => {
      console.error('SSE connection error:', error);
      removeConnection(res);
    });

    // Keep connection alive with periodic heartbeat
    const heartbeat = setInterval(() => {
      try {
        res.write(`data: ${JSON.stringify({
          type: 'heartbeat',
          timestamp: new Date().toISOString()
        })}\n\n`);
      } catch (error) {
        clearInterval(heartbeat);
        removeConnection(res);
      }
    }, 30000); // Send heartbeat every 30 seconds

    // Clean up on connection close
    res.on('close', () => {
      clearInterval(heartbeat);
      removeConnection(res);
    });

  } catch (error) {
    console.error('Error in lobby events SSE:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
