import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { setConnections, broadcastLobbyEvent } from '../../../src/utils/eventBroadcaster';

// Store active connections
const connections = new Set<NextApiResponse>();

// Set the connections in the broadcaster
setConnections(connections);

// Re-export for backward compatibility
export { broadcastLobbyEvent };

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Set up SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    });

    // Send initial connection message
    res.write(`data: ${JSON.stringify({
      type: 'connected',
      message: 'Connected to lobby events',
      timestamp: new Date().toISOString()
    })}\n\n`);

    // Add this connection to the set
    connections.add(res);

    // Handle client disconnect
    req.on('close', () => {
      connections.delete(res);
    });

    req.on('end', () => {
      connections.delete(res);
    });

    // Keep connection alive with periodic heartbeat
    const heartbeat = setInterval(() => {
      try {
        res.write(`data: ${JSON.stringify({
          type: 'heartbeat',
          timestamp: new Date().toISOString()
        })}\n\n`);
      } catch (error) {
        clearInterval(heartbeat);
        connections.delete(res);
      }
    }, 30000); // Send heartbeat every 30 seconds

    // Clean up on connection close
    res.on('close', () => {
      clearInterval(heartbeat);
      connections.delete(res);
    });

  } catch (error) {
    console.error('Error in lobby events SSE:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
