import type { NextApiRequest, NextApiResponse } from 'next';
import { broadcastLobbyEvent } from '../../src/utils/eventBroadcaster';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Broadcast a test event
    broadcastLobbyEvent({
      type: 'lobby_created',
      lobbyId: 'test-lobby-123',
      data: { message: 'This is a test broadcast' },
      timestamp: new Date().toISOString()
    });

    res.status(200).json({ 
      success: true, 
      message: 'Test event broadcasted' 
    });
  } catch (error) {
    console.error('Error broadcasting test event:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to broadcast test event' 
    });
  }
}
