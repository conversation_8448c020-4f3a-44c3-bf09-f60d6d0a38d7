import type { NextApiRequest, NextApiResponse } from 'next/types';
import { connectToDatabase } from '../../lib/mongodb';
import webpush from 'web-push';
import { DiscordMessage } from '../../src/types/discord';

// Configure web-push with VAPID keys
webpush.setVapidDetails(
  'mailto:<EMAIL>', // Replace with your email
  process.env.NEXT_PUBLIC_VAPID_KEY || '',
  process.env.VAPID_PRIVATE_KEY || ''
);

// Define an interface for WebPush errors
interface WebPushError extends Error {
  statusCode?: number;
  endpoint?: string;
  body?: string;
  headers?: Record<string, string>;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Verify API key for security
  const authHeader = req.headers.authorization;
  console.log('Auth header received:', authHeader ? 'Bearer [redacted]' : 'None');
  console.log('Expected API key:', process.env.API_SECRET_KEY ? '[redacted first 3 chars]' + '...' : 'Not set');

  if (!authHeader || !authHeader.startsWith('Bearer ') || authHeader.split(' ')[1] !== process.env.API_SECRET_KEY) {
    console.error('Unauthorized notification request');
    console.error('API_SECRET_KEY environment variable is set:', !!process.env.API_SECRET_KEY);
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const { message } = req.body as { message: DiscordMessage };
    
    if (!message) {
      console.error('No message provided in notification request');
      return res.status(400).json({ error: 'No message provided' });
    }

    console.log('Processing notification for message:', message.id);

    let db;
    try {
      // Connect to the database
      const connection = await connectToDatabase();
      db = connection.db;
    } catch (dbError) {
      console.error('MongoDB connection error:', dbError);
      // Return success but with 0 notifications sent
      return res.status(200).json({
        success: true,
        sent: 0,
        total: 0,
        error: 'Database connection failed'
      });
    }
    
    // Get all active subscriptions
    const subscriptions = await db.collection('push_subscriptions')
      .find({})
      .toArray();

    console.log(`Found ${subscriptions.length} push subscriptions`);

    if (!subscriptions || subscriptions.length === 0) {
      return res.status(200).json({
        success: true,
        sent: 0,
        total: 0,
        message: 'No subscriptions found'
      });
    }

    // Send notifications to all subscribers
    const notificationPromises = subscriptions.map(async (sub) => {
      try {
        const payload = JSON.stringify({
          title: `New message in #${message.channelName}`,
          content: message.content || 'New Discord message',
          author: message.author?.username || 'Discord User',
          imageUrl: message.imageUrl,
          channelName: message.channelName,
          url: '/discord',
          timestamp: Date.now() // Add timestamp for freshness
        });

        console.log(`Sending notification to subscription: ${sub.subscription.endpoint.substring(0, 30)}...`);
        console.log('Notification payload:', payload);
        
        await webpush.sendNotification(
          sub.subscription,
          payload
        );
        return { success: true, endpoint: sub.subscription.endpoint };
      } catch (error) {
        console.error('Error sending notification:', error);
        
        // Type guard to check if error is a WebPushError
        const webPushError = error as WebPushError;
        
        // If subscription is no longer valid, remove it
        if (webPushError.statusCode === 404 || webPushError.statusCode === 410) {
          try {
            await db.collection('push_subscriptions').deleteOne({
              'subscription.endpoint': sub.subscription.endpoint
            });
            console.log(`Removed invalid subscription: ${sub.subscription.endpoint.substring(0, 30)}...`);
          } catch (dbError) {
            console.error('Error removing invalid subscription:', dbError);
          }
        }
        
        return { success: false, endpoint: sub.subscription.endpoint, error: webPushError.message || 'Unknown error' };
      }
    });

    const results = await Promise.all(notificationPromises);
    const successCount = results.filter(r => r.success).length;

    console.log(`Successfully sent ${successCount} out of ${subscriptions.length} notifications`);

    return res.status(200).json({
      success: true,
      sent: successCount,
      total: subscriptions.length
    });
  } catch (error) {
    console.error('Error in send-notifications API:', error);
    return res.status(500).json({ 
      error: 'Failed to send notifications',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}






