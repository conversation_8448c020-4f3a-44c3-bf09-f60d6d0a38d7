import Discord from 'discord.js';
import discordConfig from '../config/discord';

// Helper function to handle different Discord.js versions
function getIntents() {
  return [
    Discord.GatewayIntentBits.Guilds,
    Discord.GatewayIntentBits.GuildMessages,
    Discord.GatewayIntentBits.MessageContent,
  ];
}

// Singleton pattern for Discord client
class DiscordClientManager {
  private static instance: DiscordClientManager;
  private client: Discord.Client | null = null;
  private isConnected = false;
  private guildRoles: Map<string, { name: string, color: string }> = new Map();

  private constructor() {}

  public static getInstance(): DiscordClientManager {
    if (!DiscordClientManager.instance) {
      DiscordClientManager.instance = new DiscordClientManager();
    }
    return DiscordClientManager.instance;
  }

  public getClient(): Discord.Client {
    if (!this.client) {
      this.client = new Discord.Client({ intents: getIntents() });
    }
    return this.client;
  }

  public async connect(): Promise<void> {
    if (!this.isConnected) {
      try {
        await this.getClient().login(discordConfig.token);
        this.isConnected = true;
        console.log('Discord bot connected');
        
        // Fetch and cache guild roles
        const guild = await this.client!.guilds.fetch(discordConfig.guildId);
        const roles = await guild.roles.fetch();
        
        // Clear and update roles cache
        this.guildRoles.clear();
        roles.forEach(role => {
          this.guildRoles.set(role.id, { 
            name: role.name, 
            color: role.hexColor 
          });
        });
        
        console.log(`Cached ${this.guildRoles.size} roles from guild`);
      } catch (error) {
        console.error('Failed to connect Discord bot:', error);
        this.isConnected = false;
        throw error;
      }
    }
  }

  public getRoles(): Map<string, { name: string, color: string }> {
    return this.guildRoles;
  }

  public isClientConnected(): boolean {
    return this.isConnected;
  }
}

export function formatMessageContent(content: string): string {
  // Replace role mentions, user mentions, etc.
  return content
    .replace(/<@&(\d+)>/g, (match, roleId) => {
      const role = DiscordClientManager.getInstance().getRoles().get(roleId);
      return role ? `@${role.name}` : match;
    })
    .replace(/<@!?(\d+)>/g, '@user')
    .replace(/<#(\d+)>/g, '#channel');
}

export function cleanMessageContent(content: string, imageUrl?: string): string {
  if (!content) return '';
  
  // Remove image URLs from content
  if (imageUrl) {
    content = content.replace(imageUrl, '').trim();
  }
  
  // Remove all URLs
  content = content.replace(/https?:\/\/\S+/g, '').trim();
  
  return content;
}

export function aggressiveCleanMessageContent(content: string): string {
  // More aggressive cleaning for content with URLs
  return content
    .split('\n')
    .filter(line => !line.includes('http'))
    .join('\n')
    .trim();
}

export default DiscordClientManager.getInstance();
