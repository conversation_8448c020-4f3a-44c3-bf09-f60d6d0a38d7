import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useSession } from 'next-auth/react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Alert,
  CircularProgress,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { Delete, Add, Save } from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useLobby } from '../../src/hooks/useLobby';
import { OrganizerConfig } from '../../src/types/lobby';

const StyledContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(4),
}));

export default function OrganizersPage() {
  const { data: session, status } = useSession();
  const { getOrganizerConfig, updateOrganizerConfig, loading, error } = useLobby();
  
  const [config, setConfig] = useState<OrganizerConfig | null>(null);
  const [newDiscordId, setNewDiscordId] = useState('');
  const [saving, setSaving] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [discordIdToDelete, setDiscordIdToDelete] = useState<string | null>(null);

  const currentUserId = (session?.user as any)?.id || session?.user?.email?.split('@')[0] || 'unknown';

  const loadConfig = async () => {
    try {
      const organizerConfig = await getOrganizerConfig();
      setConfig(organizerConfig);
    } catch (err) {
      console.error('Failed to load organizer config:', err);
    }
  };

  useEffect(() => {
    if (status === 'authenticated') {
      loadConfig();
    }
  }, [status]);

  const handleAddDiscordId = () => {
    if (!newDiscordId.trim()) return;
    
    // Basic validation for Discord ID (should be numeric)
    if (!/^\d+$/.test(newDiscordId.trim())) {
      alert('Discord ID must be a numeric string');
      return;
    }

    if (config && config.discordIds.includes(newDiscordId.trim())) {
      alert('This Discord ID is already in the list');
      return;
    }

    const updatedIds = config ? [...config.discordIds, newDiscordId.trim()] : [newDiscordId.trim()];
    setConfig({
      discordIds: updatedIds,
      lastUpdated: new Date()
    });
    setNewDiscordId('');
  };

  const handleRemoveDiscordId = (discordId: string) => {
    setDiscordIdToDelete(discordId);
    setDeleteDialogOpen(true);
  };

  const confirmRemoveDiscordId = () => {
    if (!config || !discordIdToDelete) return;

    const updatedIds = config.discordIds.filter(id => id !== discordIdToDelete);
    setConfig({
      discordIds: updatedIds,
      lastUpdated: new Date()
    });
    setDeleteDialogOpen(false);
    setDiscordIdToDelete(null);
  };

  const handleSaveConfig = async () => {
    if (!config) return;

    try {
      setSaving(true);
      const updatedConfig = await updateOrganizerConfig(config.discordIds);
      setConfig(updatedConfig);
      alert('Organizer configuration saved successfully!');
    } catch (err) {
      console.error('Failed to save config:', err);
    } finally {
      setSaving(false);
    }
  };

  if (status === 'loading') {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
        <CircularProgress />
      </Box>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <StyledContainer maxWidth="md">
        <Alert severity="warning">
          Please sign in with Discord to access the admin panel.
        </Alert>
      </StyledContainer>
    );
  }

  return (
    <>
      <Head>
        <title>Organizer Management - Loapals Admin</title>
        <meta name="description" content="Manage lobby organizer permissions" />
      </Head>

      <StyledContainer maxWidth="md">
        <Typography variant="h4" component="h1" gutterBottom>
          Organizer Management
        </Typography>

        <Typography variant="body1" color="text.secondary" paragraph>
          Manage which Discord users can create and organize game lobbies. Only users listed here will have permission to create new lobbies.
        </Typography>

        {loading && !config ? (
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress />
          </Box>
        ) : (
          <Card>
            <CardHeader 
              title="Authorized Organizers"
              subheader={`${config?.discordIds.length || 0} organizers configured`}
            />
            <CardContent>
              <Box mb={3}>
                <Typography variant="h6" gutterBottom>
                  Add New Organizer
                </Typography>
                <Box display="flex" gap={2} alignItems="center">
                  <TextField
                    label="Discord User ID"
                    value={newDiscordId}
                    onChange={(e) => setNewDiscordId(e.target.value)}
                    placeholder="Enter Discord user ID (numeric)"
                    fullWidth
                    helperText="You can find Discord IDs by enabling Developer Mode in Discord and right-clicking on a user"
                  />
                  <Button
                    variant="contained"
                    startIcon={<Add />}
                    onClick={handleAddDiscordId}
                    disabled={!newDiscordId.trim()}
                  >
                    Add
                  </Button>
                </Box>
              </Box>

              {config && config.discordIds.length > 0 ? (
                <>
                  <Typography variant="h6" gutterBottom>
                    Current Organizers
                  </Typography>
                  <List>
                    {config.discordIds.map((discordId) => (
                      <ListItem key={discordId} divider>
                        <ListItemText
                          primary={discordId}
                          secondary={discordId === currentUserId ? 'You' : 'Discord User'}
                        />
                        <ListItemSecondaryAction>
                          {discordId === currentUserId && (
                            <Chip label="You" color="primary" size="small" sx={{ mr: 1 }} />
                          )}
                          <IconButton
                            edge="end"
                            onClick={() => handleRemoveDiscordId(discordId)}
                            color="error"
                          >
                            <Delete />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                </>
              ) : (
                <Alert severity="info">
                  No organizers configured yet. Add Discord user IDs to allow users to create lobbies.
                </Alert>
              )}

              {config && (
                <Box mt={3} display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2" color="text.secondary">
                    Last updated: {new Date(config.lastUpdated).toLocaleString()}
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={saving ? <CircularProgress size={20} /> : <Save />}
                    onClick={handleSaveConfig}
                    disabled={saving}
                  >
                    {saving ? 'Saving...' : 'Save Configuration'}
                  </Button>
                </Box>
              )}

              {error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {error}
                </Alert>
              )}
            </CardContent>
          </Card>
        )}

        <Card sx={{ mt: 3 }}>
          <CardHeader title="How to Find Discord User IDs" />
          <CardContent>
            <Typography variant="body2" paragraph>
              To find a Discord user's ID:
            </Typography>
            <ol>
              <li>Enable Developer Mode in Discord (User Settings → Advanced → Developer Mode)</li>
              <li>Right-click on the user's profile or message</li>
              <li>Select "Copy ID" from the context menu</li>
              <li>Paste the ID here (it will be a long number)</li>
            </ol>
          </CardContent>
        </Card>
      </StyledContainer>

      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Remove Organizer</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to remove Discord ID "{discordIdToDelete}" from the organizers list?
            They will no longer be able to create new lobbies.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmRemoveDiscordId} color="error" variant="contained">
            Remove
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
