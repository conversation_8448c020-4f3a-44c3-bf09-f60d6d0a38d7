import { useSession, signIn, signOut } from 'next-auth/react';
import { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';

export default function AuthTest() {
  const { data: session, status } = useSession();
  const [authConfig, setAuthConfig] = useState<any>(null);
  const [configLoading, setConfigLoading] = useState(true);

  useEffect(() => {
    // Check auth configuration
    fetch('/api/check-auth-config')
      .then(res => res.json())
      .then(data => {
        setAuthConfig(data);
        setConfigLoading(false);
      })
      .catch(err => {
        console.error('Failed to check auth config:', err);
        setConfigLoading(false);
      });
  }, []);

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Authentication Test Page
      </Typography>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Session Status: {status}
          </Typography>
          
          {status === 'loading' && (
            <Box display="flex" alignItems="center" gap={2}>
              <CircularProgress size={20} />
              <Typography>Loading session...</Typography>
            </Box>
          )}

          {status === 'authenticated' && session && (
            <Box>
              <Alert severity="success" sx={{ mb: 2 }}>
                Successfully authenticated!
              </Alert>
              <Typography variant="subtitle1" gutterBottom>
                User Information:
              </Typography>
              <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
                {JSON.stringify(session, null, 2)}
              </pre>
              <Button 
                variant="outlined" 
                color="error" 
                onClick={() => signOut()}
                sx={{ mt: 2 }}
              >
                Sign Out
              </Button>
            </Box>
          )}

          {status === 'unauthenticated' && (
            <Box>
              <Alert severity="info" sx={{ mb: 2 }}>
                Not authenticated
              </Alert>
              <Button 
                variant="contained" 
                onClick={() => signIn('discord')}
              >
                Sign In with Discord
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Authentication Configuration
          </Typography>
          
          {configLoading ? (
            <Box display="flex" alignItems="center" gap={2}>
              <CircularProgress size={20} />
              <Typography>Checking configuration...</Typography>
            </Box>
          ) : authConfig ? (
            <Box>
              <Typography variant="body2" component="div">
                <strong>Discord Client ID:</strong> {authConfig.hasDiscordClientId ? '✅ Set' : '❌ Missing'} 
                {authConfig.hasDiscordClientId && ` (${authConfig.discordClientIdLength} chars)`}
              </Typography>
              <Typography variant="body2" component="div">
                <strong>Discord Client Secret:</strong> {authConfig.hasDiscordClientSecret ? '✅ Set' : '❌ Missing'}
              </Typography>
              <Typography variant="body2" component="div">
                <strong>NextAuth Secret:</strong> {authConfig.hasNextAuthSecret ? '✅ Set' : '❌ Missing'}
                {authConfig.hasNextAuthSecret && ` (${authConfig.nextAuthSecretLength} chars)`}
              </Typography>
              <Typography variant="body2" component="div">
                <strong>NextAuth URL:</strong> {authConfig.hasNextAuthUrl ? '✅ Set' : '❌ Missing'}
              </Typography>
              <Typography variant="body2" component="div">
                <strong>Node Environment:</strong> {authConfig.nodeEnv}
              </Typography>

              <Divider sx={{ my: 2 }} />

              {!authConfig.hasDiscordClientId && (
                <Alert severity="error" sx={{ mb: 1 }}>
                  DISCORD_CLIENT_ID is missing from environment variables
                </Alert>
              )}
              {!authConfig.hasDiscordClientSecret && (
                <Alert severity="error" sx={{ mb: 1 }}>
                  DISCORD_CLIENT_SECRET is missing from environment variables
                </Alert>
              )}
              {!authConfig.hasNextAuthSecret && (
                <Alert severity="error" sx={{ mb: 1 }}>
                  NEXTAUTH_SECRET is missing from environment variables
                </Alert>
              )}
              {!authConfig.hasNextAuthUrl && (
                <Alert severity="warning" sx={{ mb: 1 }}>
                  NEXTAUTH_URL is missing (optional but recommended)
                </Alert>
              )}
            </Box>
          ) : (
            <Alert severity="error">
              Failed to load configuration
            </Alert>
          )}
        </CardContent>
      </Card>

      <Box mt={3}>
        <Typography variant="body2" color="text.secondary">
          If you're seeing authentication issues:
        </Typography>
        <ul>
          <li>Make sure all environment variables are set correctly</li>
          <li>Restart your development server after changing environment variables</li>
          <li>Check the browser console for any errors</li>
          <li>Verify your Discord application settings</li>
        </ul>
      </Box>
    </Container>
  );
}
