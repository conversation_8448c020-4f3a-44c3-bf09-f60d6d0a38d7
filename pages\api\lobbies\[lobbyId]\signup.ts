import type { NextApiRequest, NextApiResponse } from 'next/types';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { LobbyService } from '../../../../src/services/lobbyService';
import { SignupRequest, SignupResponse } from '../../../../src/types/lobby';
import { canUserSignUp } from '../../../../src/utils/lobby';

const lobbyService = new LobbyService();

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  const { lobbyId } = req.query;

  if (!lobbyId || typeof lobbyId !== 'string') {
    return res.status(400).json({ 
      success: false, 
      error: 'Invalid lobby ID' 
    });
  }

  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session || !session.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const discordUser = {
      id: (session.user as any).id || session.user.email?.split('@')[0] || 'unknown',
      username: session.user.name || 'Unknown User',
      discriminator: '0000',
      avatar: session.user.image || undefined,
      email: session.user.email || undefined
    };

    if (req.method === 'POST') {
      // Sign up for lobby
      const { slotPosition }: SignupRequest = req.body;

      if (!slotPosition || !Number.isInteger(slotPosition) || slotPosition < 1 || slotPosition > 8) {
        const response: SignupResponse = {
          success: false,
          error: 'Invalid slot position. Must be between 1 and 8.'
        };
        return res.status(400).json(response);
      }

      // Get the lobby to validate signup
      const lobby = await lobbyService.getLobbyById(lobbyId);
      if (!lobby) {
        const response: SignupResponse = {
          success: false,
          error: 'Lobby not found'
        };
        return res.status(404).json(response);
      }

      // Check if user can sign up
      const validation = canUserSignUp(lobby, discordUser.id, slotPosition);
      if (!validation.canSignUp) {
        const response: SignupResponse = {
          success: false,
          error: validation.reason
        };
        return res.status(400).json(response);
      }

      try {
        const result = await lobbyService.signUpForLobby(lobbyId, discordUser, slotPosition);

        if (result.success) {
          const response: SignupResponse = {
            success: true,
            signup: {
              lobbyId,
              userId: discordUser.id,
              user: discordUser,
              slotPosition,
              signedUpAt: new Date()
            }
          };
          return res.status(200).json(response);
        } else {
          const response: SignupResponse = {
            success: false,
            error: result.error
          };
          return res.status(400).json(response);
        }
      } catch (error) {
        console.error('Error signing up for lobby:', error);
        const response: SignupResponse = {
          success: false,
          error: 'Failed to sign up for lobby'
        };
        return res.status(500).json(response);
      }
    }

    if (req.method === 'DELETE') {
      // Remove signup from lobby
      try {
        const result = await lobbyService.removeSignup(lobbyId, discordUser.id);
        
        if (result.success) {
          return res.status(200).json({ 
            success: true, 
            message: 'Successfully removed from lobby' 
          });
        } else {
          return res.status(400).json({ 
            success: false, 
            error: result.error 
          });
        }
      } catch (error) {
        console.error('Error removing signup:', error);
        return res.status(500).json({ 
          success: false, 
          error: 'Failed to remove signup' 
        });
      }
    }

    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });

  } catch (error) {
    console.error('Error in signup API:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
}
