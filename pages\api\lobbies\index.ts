import type { NextApiRequest, NextApiResponse } from 'next/types';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { LobbyService } from '../../../src/services/lobbyService';
import { validateLobbyData } from '../../../src/utils/lobby';
import { CreateLobbyRequest, CreateLobbyResponse, LobbiesResponse } from '../../../src/types/lobby';

const lobbyService = new LobbyService();

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session || !session.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const discordUser = {
      id: (session.user as any).discordId || (session.user as any).id || 'unknown',
      username: session.user.name || 'Unknown User',
      discriminator: '0000',
      avatar: session.user.image || undefined,
      email: undefined // Email not collected from Discord OAuth
    };

    if (req.method === 'GET') {
      // Get lobbies
      const { status, organizerId, limit } = req.query;
      
      const lobbies = await lobbyService.getLobbies(
        status as any,
        organizerId as string,
        limit ? parseInt(limit as string) : undefined
      );

      const response: LobbiesResponse = {
        success: true,
        lobbies
      };

      return res.status(200).json(response);
    }

    if (req.method === 'POST') {
      // Create lobby
      const { title, description, scheduledTime, maxPassengers }: CreateLobbyRequest = req.body;

      // Validate input
      const validation = validateLobbyData({ title, scheduledTime, maxPassengers });
      if (!validation.isValid) {
        const response: CreateLobbyResponse = {
          success: false,
          error: validation.errors.join(', ')
        };
        return res.status(400).json(response);
      }

      // Check if user is authorized to create lobbies
      const isOrganizer = await lobbyService.isUserOrganizer(discordUser.id);
      if (!isOrganizer) {
        const response: CreateLobbyResponse = {
          success: false,
          error: 'You are not authorized to create lobbies'
        };
        return res.status(403).json(response);
      }

      try {
        const lobby = await lobbyService.createLobby(
          title,
          description,
          new Date(scheduledTime),
          maxPassengers,
          discordUser
        );

        const response: CreateLobbyResponse = {
          success: true,
          lobby
        };

        return res.status(201).json(response);
      } catch (error) {
        console.error('Error creating lobby:', error);
        const response: CreateLobbyResponse = {
          success: false,
          error: 'Failed to create lobby'
        };
        return res.status(500).json(response);
      }
    }

    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });

  } catch (error) {
    console.error('Error in lobbies API:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
}
