import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useSession, signIn } from 'next-auth/react';
import {
  Box,
  Container,
  Typography,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent
} from '@mui/material';

export default function LobbiesSimplePage() {
  const { data: session, status } = useSession();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  console.log('Session status:', status);
  console.log('Session data:', session);

  return (
    <>
      <Head>
        <title>Game Lobbies (Simple) - Loapals</title>
      </Head>

      <Container maxWidth="md" sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Game Lobbies (Simple Test)
        </Typography>

        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Authentication Status
            </Typography>
            
            <Typography variant="body1" gutterBottom>
              Status: <strong>{status}</strong>
            </Typography>

            {status === 'loading' && (
              <Box display="flex" alignItems="center" gap={2} mt={2}>
                <CircularProgress size={20} />
                <Typography>Loading session...</Typography>
              </Box>
            )}

            {status === 'authenticated' && session && (
              <Box mt={2}>
                <Alert severity="success" sx={{ mb: 2 }}>
                  Successfully authenticated!
                </Alert>
                <Typography variant="body2">
                  Welcome, {session.user?.name || 'Discord User'}!
                </Typography>
                <Typography variant="body2">
                  User ID: {(session.user as any)?.id || 'Not available'}
                </Typography>
                <Typography variant="body2">
                  Email: {session.user?.email || 'Not available'}
                </Typography>
              </Box>
            )}

            {status === 'unauthenticated' && (
              <Box mt={2}>
                <Alert severity="info" sx={{ mb: 2 }}>
                  Please sign in to access game lobbies
                </Alert>
                <Button 
                  variant="contained" 
                  onClick={() => signIn('discord')}
                >
                  Sign In with Discord
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>

        {status === 'authenticated' && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Lobby System
              </Typography>
              <Typography variant="body1" paragraph>
                The lobby system is ready! Once authentication is working properly, 
                you'll be able to create and join game lobbies here.
              </Typography>
              <Button 
                variant="outlined" 
                href="/lobbies"
                disabled={status !== 'authenticated'}
              >
                Go to Full Lobby System
              </Button>
            </CardContent>
          </Card>
        )}

        <Box mt={3}>
          <Typography variant="body2" color="text.secondary">
            Debug Information:
          </Typography>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto'
          }}>
            {JSON.stringify({ 
              status, 
              hasSession: !!session,
              userId: (session?.user as any)?.id,
              userName: session?.user?.name,
              timestamp: new Date().toISOString()
            }, null, 2)}
          </pre>
        </Box>
      </Container>
    </>
  );
}
