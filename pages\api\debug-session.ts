import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';

const ADMIN_DISCORD_IDS = process.env.ADMIN_DISCORD_IDS?.split(',') || [];

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session) {
      return res.status(401).json({
        error: 'No session found',
        authenticated: false
      });
    }

    const discordUserId = (session.user as any).id || session.user.email?.split('@')[0] || 'unknown';
    
    return res.status(200).json({
      authenticated: true,
      session: {
        user: session.user,
        expires: session.expires,
        accessToken: session.accessToken ? 'Present' : 'Missing'
      },
      extractedDiscordId: discordUserId,
      adminDiscordIds: ADMIN_DISCORD_IDS,
      isAdmin: ADMIN_DISCORD_IDS.includes(discordUserId),
      adminCheck: {
        hasAdminIds: ADMIN_DISCORD_IDS.length > 0,
        adminIdsCount: ADMIN_DISCORD_IDS.length,
        userIdType: typeof discordUserId,
        userIdLength: discordUserId.length,
        exactMatch: ADMIN_DISCORD_IDS.find(id => id === discordUserId),
        trimmedMatch: ADMIN_DISCORD_IDS.find(id => id.trim() === discordUserId.trim())
      }
    });
  } catch (error) {
    console.error('Debug session error:', error);
    return res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error',
      authenticated: false
    });
  }
}
