import NextAuth, { NextAuthOptions } from "next-auth";
import Discord<PERSON>rovider from "next-auth/providers/discord";

export const authOptions: NextAuthOptions = {
  providers: [
    DiscordProvider({
      clientId: process.env.DISCORD_CLIENT_ID!,
      clientSecret: process.env.DISCORD_CLIENT_SECRET!,
      authorization: { params: { scope: "identify" } },
      profile(profile) {
        console.log('Discord Profile received:', profile);
        return {
          id: profile.id,
          name: profile.username,
          email: null, // Email not requested from Discord
          image: profile.avatar ? `https://cdn.discordapp.com/avatars/${profile.id}/${profile.avatar}.png` : null,
        };
      },
    }),
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: '/',      // Redirect sign-in to homepage
    error: '/auth/error', // Custom error page
  },
  callbacks: {
    async jwt({ token, account, profile, user }) {
      // Persist the OAuth access_token and or the user id to the token right after signin
      if (account && profile) {
        console.log('JWT Callback - Account:', account);
        console.log('JWT Callback - Profile:', profile);
        console.log('JWT Callback - User:', user);
        token.accessToken = account.access_token;
        token.id = profile.id;
        token.discordId = profile.id; // Explicitly store Discord ID
        console.log('JWT Callback - Token ID set to:', token.id);
        console.log('JWT Callback - Discord ID set to:', token.discordId);
      }
      return token;
    },
    async session({ session, token }) {
      // Send properties to the client, like an access_token and user id from a provider.
      if (token.accessToken) {
        session.accessToken = token.accessToken;
      }
      if (token.id) {
        (session.user as any).id = token.id;
        (session.user as any).discordId = token.discordId || token.id;
        console.log('Session Callback - User ID set to:', token.id);
        console.log('Session Callback - Discord ID set to:', token.discordId || token.id);
      }
      console.log('Session Callback - Final session user:', session.user);
      return session;
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === 'development',
};

export default NextAuth(authOptions);