import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { 
  GameLobby, 
  CreateLobbyRequest, 
  CreateLobbyResponse, 
  LobbiesResponse, 
  LobbyResponse,
  SignupRequest,
  SignupResponse,
  OrganizerConfig
} from '../types/lobby';

export function useLobby() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    return response.json();
  }, []);

  const createLobby = useCallback(async (lobbyData: CreateLobbyRequest): Promise<GameLobby> => {
    setLoading(true);
    setError(null);

    try {
      const response: CreateLobbyResponse = await apiCall('/api/lobbies', {
        method: 'POST',
        body: JSON.stringify(lobbyData),
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to create lobby');
      }

      return response.lobby!;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create lobby';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const getLobbies = useCallback(async (filters?: {
    status?: GameLobby['status'];
    organizerId?: string;
    limit?: number;
  }): Promise<GameLobby[]> => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (filters?.status) params.append('status', filters.status);
      if (filters?.organizerId) params.append('organizerId', filters.organizerId);
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const url = `/api/lobbies${params.toString() ? `?${params.toString()}` : ''}`;
      const response: LobbiesResponse = await apiCall(url);

      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch lobbies');
      }

      return response.lobbies || [];
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch lobbies';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const getLobby = useCallback(async (lobbyId: string): Promise<GameLobby> => {
    setLoading(true);
    setError(null);

    try {
      const response: LobbyResponse = await apiCall(`/api/lobbies/${lobbyId}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch lobby');
      }

      return response.lobby!;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch lobby';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const updateLobby = useCallback(async (
    lobbyId: string, 
    updates: Partial<Pick<GameLobby, 'title' | 'description' | 'scheduledTime' | 'status'>>
  ): Promise<GameLobby> => {
    setLoading(true);
    setError(null);

    try {
      const response: LobbyResponse = await apiCall(`/api/lobbies/${lobbyId}`, {
        method: 'PUT',
        body: JSON.stringify(updates),
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to update lobby');
      }

      return response.lobby!;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update lobby';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const deleteLobby = useCallback(async (lobbyId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await apiCall(`/api/lobbies/${lobbyId}`, {
        method: 'DELETE',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete lobby';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const signUpForLobby = useCallback(async (lobbyId: string, slotPosition: number): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      const signupData: SignupRequest = { lobbyId, slotPosition };
      const response: SignupResponse = await apiCall(`/api/lobbies/${lobbyId}/signup`, {
        method: 'POST',
        body: JSON.stringify(signupData),
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to sign up for lobby');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to sign up for lobby';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const removeSignup = useCallback(async (lobbyId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await apiCall(`/api/lobbies/${lobbyId}/signup`, {
        method: 'DELETE',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove signup';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const kickUserFromLobby = useCallback(async (lobbyId: string, userId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await apiCall(`/api/lobbies/${lobbyId}/kick`, {
        method: 'POST',
        body: JSON.stringify({ userId }),
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to kick user';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const getOrganizerConfig = useCallback(async (): Promise<OrganizerConfig> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiCall('/api/organizers');

      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch organizer config');
      }

      return response.config;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch organizer config';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const updateOrganizerConfig = useCallback(async (discordIds: string[]): Promise<OrganizerConfig> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiCall('/api/organizers', {
        method: 'POST',
        body: JSON.stringify({ discordIds }),
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to update organizer config');
      }

      return response.config;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update organizer config';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  return {
    loading,
    error,
    createLobby,
    getLobbies,
    getLobby,
    updateLobby,
    deleteLobby,
    signUpForLobby,
    removeSignup,
    kickUserFromLobby,
    getOrganizerConfig,
    updateOrganizerConfig,
    isAuthenticated: !!session,
    user: session?.user
  };
}
