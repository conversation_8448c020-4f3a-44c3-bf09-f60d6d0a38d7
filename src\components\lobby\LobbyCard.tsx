import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardA<PERSON>,
  Typography,
  Button,
  Box,
  Chip,
  Avatar,
  Grid,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert
} from '@mui/material';
import {
  AccessTime,
  Person,
  MoreVert,
  Edit,
  Delete,
  ExitToApp
} from '@mui/icons-material';
import { GameLobby } from '../../types/lobby';
import { formatLobbyTime, getLobbyStatusColor, isUserSignedUp, getUserDriverPosition } from '../../utils/lobby';
import { useLobby } from '../../hooks/useLobby';
import { useSession } from 'next-auth/react';

interface LobbyCardProps {
  lobby: GameLobby;
  onLobbyUpdated?: (lobby: GameLobby) => void;
  onLobbyDeleted?: (lobbyId: string) => void;
  onSignupChanged?: () => void;
}

export default function LobbyCard({ 
  lobby, 
  onLobbyUpdated, 
  onLobbyDeleted, 
  onSignupChanged 
}: LobbyCardProps) {
  const { data: session } = useSession();
  const { signUpForLobby, removeSignup, deleteLobby, loading, error } = useLobby();
  
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedDriverPosition, setSelectedDriverPosition] = useState<number | null>(null);

  const currentUserId = (session?.user as any)?.id || session?.user?.email?.split('@')[0] || 'unknown';
  const isOrganizer = lobby.organizerId === currentUserId;
  const userSignedUp = isUserSignedUp(lobby, currentUserId);
  const userDriverPosition = getUserDriverPosition(lobby, currentUserId);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleSignUp = async (driverPosition: number) => {
    try {
      await signUpForLobby(lobby.id, driverPosition);
      if (onSignupChanged) {
        onSignupChanged();
      }
    } catch (err) {
      console.error('Failed to sign up:', err);
    }
  };

  const handleRemoveSignup = async () => {
    try {
      await removeSignup(lobby.id);
      if (onSignupChanged) {
        onSignupChanged();
      }
    } catch (err) {
      console.error('Failed to remove signup:', err);
    }
  };

  const handleDeleteLobby = async () => {
    try {
      await deleteLobby(lobby.id);
      if (onLobbyDeleted) {
        onLobbyDeleted(lobby.id);
      }
    } catch (err) {
      console.error('Failed to delete lobby:', err);
    }
    setDeleteDialogOpen(false);
  };

  const getAvailablePositions = () => {
    return lobby.drivers
      .filter(driver => !driver.user)
      .map(driver => driver.position);
  };

  const getStatusChipColor = () => {
    switch (lobby.status) {
      case 'open': return 'success';
      case 'full': return 'warning';
      case 'started': return 'info';
      case 'completed': return 'default';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  return (
    <>
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <CardContent sx={{ flexGrow: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
            <Typography variant="h6" component="h3" gutterBottom>
              {lobby.title}
            </Typography>
            <Box display="flex" alignItems="center" gap={1}>
              <Chip 
                label={lobby.status.toUpperCase()} 
                color={getStatusChipColor() as any}
                size="small"
              />
              {(isOrganizer || userSignedUp) && (
                <IconButton size="small" onClick={handleMenuOpen}>
                  <MoreVert />
                </IconButton>
              )}
            </Box>
          </Box>

          {lobby.description && (
            <Typography variant="body2" color="text.secondary" paragraph>
              {lobby.description}
            </Typography>
          )}

          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <AccessTime fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary">
              {formatLobbyTime(new Date(lobby.scheduledTime))}
            </Typography>
          </Box>

          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <Person fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary">
              Organizer: {lobby.organizer.username}
            </Typography>
          </Box>

          <Typography variant="subtitle2" gutterBottom>
            Drivers ({lobby.drivers.filter(d => d.user).length}/{lobby.maxDrivers}):
          </Typography>
          
          <Grid container spacing={1}>
            {lobby.drivers.map((driver) => (
              <Grid item xs={6} sm={4} md={3} key={driver.position}>
                <Box 
                  display="flex" 
                  alignItems="center" 
                  gap={1}
                  p={1}
                  border={1}
                  borderColor="divider"
                  borderRadius={1}
                  bgcolor={driver.user ? 'action.hover' : 'background.default'}
                >
                  <Typography variant="body2" fontWeight="bold">
                    {driver.position}:
                  </Typography>
                  {driver.user ? (
                    <Box display="flex" alignItems="center" gap={0.5}>
                      <Avatar 
                        src={driver.user.avatar} 
                        sx={{ width: 20, height: 20 }}
                      >
                        {driver.user.username[0]}
                      </Avatar>
                      <Typography variant="body2" noWrap>
                        {driver.user.username}
                      </Typography>
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      Open
                    </Typography>
                  )}
                </Box>
              </Grid>
            ))}
          </Grid>

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </CardContent>

        <CardActions>
          {!userSignedUp && lobby.status === 'open' && getAvailablePositions().length > 0 && (
            <Box display="flex" gap={1} flexWrap="wrap">
              {getAvailablePositions().map(position => (
                <Button
                  key={position}
                  size="small"
                  variant="outlined"
                  onClick={() => handleSignUp(position)}
                  disabled={loading}
                >
                  Join as Driver {position}
                </Button>
              ))}
            </Box>
          )}

          {userSignedUp && (
            <Button
              size="small"
              variant="outlined"
              color="warning"
              startIcon={<ExitToApp />}
              onClick={handleRemoveSignup}
              disabled={loading}
            >
              Leave Lobby (Driver {userDriverPosition})
            </Button>
          )}
        </CardActions>
      </Card>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {isOrganizer && [
          <MenuItem key="edit" onClick={handleMenuClose}>
            <Edit fontSize="small" sx={{ mr: 1 }} />
            Edit Lobby
          </MenuItem>,
          <MenuItem 
            key="delete" 
            onClick={() => {
              handleMenuClose();
              setDeleteDialogOpen(true);
            }}
          >
            <Delete fontSize="small" sx={{ mr: 1 }} />
            Delete Lobby
          </MenuItem>
        ]}
        {userSignedUp && !isOrganizer && (
          <MenuItem onClick={() => {
            handleMenuClose();
            handleRemoveSignup();
          }}>
            <ExitToApp fontSize="small" sx={{ mr: 1 }} />
            Leave Lobby
          </MenuItem>
        )}
      </Menu>

      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Lobby</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{lobby.title}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleDeleteLobby} 
            color="error" 
            variant="contained"
            disabled={loading}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
