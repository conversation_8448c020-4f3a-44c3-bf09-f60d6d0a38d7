import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardA<PERSON>,
  Typography,
  Button,
  Box,
  Chip,
  Avatar,
  Grid,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert
} from '@mui/material';
import {
  AccessTime,
  Person,
  MoreVert,
  Edit,
  Delete,
  ExitToApp,
  PersonRemove
} from '@mui/icons-material';
import { GameLobby } from '../../types/lobby';
import { formatLobbyTime, getLobbyStatusColor, isUserSignedUp, getUserSlotPosition, getAvailablePassengerPositions, getDisplayName } from '../../utils/lobby';
import { useLobby } from '../../hooks/useLobby';
import { useSession } from 'next-auth/react';

interface LobbyCardProps {
  lobby: GameLobby;
  onLobbyUpdated?: (lobby: GameLobby) => void;
  onLobbyDeleted?: (lobbyId: string) => void;
  onSignupChanged?: () => void;
}

export default function LobbyCard({
  lobby,
  onLobbyUpdated,
  onLobbyDeleted,
  onSignupChanged
}: LobbyCardProps) {
  const { data: session } = useSession();
  const { signUpForLobby, removeSignup, deleteLobby, kickUserFromLobby, loading, error } = useLobby();
  
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedDriverPosition, setSelectedDriverPosition] = useState<number | null>(null);

  const currentUserId = (session?.user as any)?.discordId || (session?.user as any)?.id || 'unknown';
  const isOrganizer = lobby.organizerId === currentUserId;
  const userSignedUp = isUserSignedUp(lobby, currentUserId);
  const userSlotPosition = getUserSlotPosition(lobby, currentUserId);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleSignUp = async (slotPosition: number) => {
    try {
      await signUpForLobby(lobby.id, slotPosition);
      if (onSignupChanged) {
        onSignupChanged();
      }
    } catch (err) {
      console.error('Failed to sign up:', err);
    }
  };

  const handleRemoveSignup = async () => {
    try {
      await removeSignup(lobby.id);
      if (onSignupChanged) {
        onSignupChanged();
      }
    } catch (err) {
      console.error('Failed to remove signup:', err);
    }
  };

  const handleKickUser = async (userId: string) => {
    try {
      await kickUserFromLobby(lobby.id, userId);
      if (onSignupChanged) {
        onSignupChanged();
      }
    } catch (err) {
      console.error('Failed to kick user:', err);
    }
  };

  const handleDeleteLobby = async () => {
    try {
      await deleteLobby(lobby.id);
      if (onLobbyDeleted) {
        onLobbyDeleted(lobby.id);
      }
    } catch (err) {
      console.error('Failed to delete lobby:', err);
    }
    setDeleteDialogOpen(false);
  };

  const getAvailablePositions = () => {
    return getAvailablePassengerPositions(lobby);
  };

  const getStatusChipColor = () => {
    switch (lobby.status) {
      case 'open': return 'success';
      case 'full': return 'warning';
      case 'started': return 'info';
      case 'completed': return 'default';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  return (
    <>
      <Card sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: 3,
        '&:hover': {
          boxShadow: 6,
          transform: 'translateY(-2px)',
          transition: 'all 0.2s ease-in-out'
        }
      }}>
        <CardContent sx={{ flexGrow: 1, p: 3 }}>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={3}>
            <Typography variant="h5" component="h3" fontWeight="bold" color="primary.main">
              {lobby.title}
            </Typography>
            <Box display="flex" alignItems="center" gap={1}>
              <Chip
                label={lobby.status.toUpperCase()}
                color={getStatusChipColor() as any}
                size="medium"
                sx={{ fontWeight: 'bold' }}
              />
              {(isOrganizer || userSignedUp) && (
                <IconButton size="small" onClick={handleMenuOpen}>
                  <MoreVert />
                </IconButton>
              )}
            </Box>
          </Box>

          {lobby.description && (
            <Typography variant="body1" color="text.secondary" paragraph sx={{ fontStyle: 'italic' }}>
              {lobby.description}
            </Typography>
          )}

          <Box display="flex" alignItems="center" gap={2} mb={2} p={2} bgcolor="background.paper" borderRadius={2} border={1} borderColor="divider">
            <AccessTime color="primary" />
            <Typography variant="body1" fontWeight="medium" color="text.primary">
              {formatLobbyTime(new Date(lobby.scheduledTime))}
            </Typography>
          </Box>

          <Box display="flex" alignItems="center" gap={2} mb={3} p={2} bgcolor="primary.light" borderRadius={2} border={1} borderColor="primary.main">
            <Person color="primary" />
            <Typography variant="body1" fontWeight="medium" color="primary.dark">
              Organizer: <strong>{lobby.organizer.username}</strong>
            </Typography>
          </Box>

          <Typography variant="subtitle2" gutterBottom>
            Raid Party (8 slots):
          </Typography>

          <Grid container spacing={3}>
            {lobby.slots.map((slot) => {
              const displayName = getDisplayName(slot, currentUserId, isOrganizer);
              const isCurrentUser = slot.user?.id === currentUserId;

              return (
                <Grid item xs={12} sm={6} md={4} lg={3} key={slot.position}>
                  <Box
                    display="flex"
                    alignItems="center"
                    gap={2}
                    p={2}
                    border={2}
                    borderColor={
                      slot.type === 'organizer' ? 'primary.main' :
                      slot.type === 'driver' ? 'warning.main' :
                      slot.type === 'passenger' && slot.user ? 'success.main' : 'grey.400'
                    }
                    borderRadius={3}
                    bgcolor={
                      slot.type === 'organizer' ? 'primary.light' :
                      slot.type === 'driver' ? 'warning.light' :
                      slot.type === 'passenger' && slot.user ? 'success.light' : 'grey.100'
                    }
                    sx={{
                      minHeight: 70,
                      width: '100%',
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 2
                      },
                      ...(isCurrentUser && {
                        boxShadow: 3,
                        borderColor: 'secondary.main',
                        bgcolor: 'secondary.light'
                      })
                    }}
                  >
                    <Box
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                      width={32}
                      height={32}
                      borderRadius="50%"
                      bgcolor={
                        slot.type === 'organizer' ? 'primary.main' :
                        slot.type === 'driver' ? 'warning.main' :
                        slot.type === 'passenger' && slot.user ? 'success.main' : 'grey.500'
                      }
                      color="white"
                      fontWeight="bold"
                      fontSize="0.875rem"
                    >
                      {slot.position}
                    </Box>

                    <Box flex={1} minWidth={0}>
                      {slot.user || slot.type === 'driver' ? (
                        <Box display="flex" alignItems="center" gap={1}>
                          {slot.user && displayName !== 'Anonymous' && (
                            <Avatar
                              src={slot.user.avatar}
                              sx={{ width: 32, height: 32 }}
                            >
                              {slot.user.username[0]}
                            </Avatar>
                          )}
                          {slot.type === 'driver' && !slot.user && (
                            <Avatar
                              sx={{
                                width: 32,
                                height: 32,
                                bgcolor: 'warning.main',
                                fontSize: '0.75rem'
                              }}
                            >
                              🛡️
                            </Avatar>
                          )}
                          <Box flex={1}>
                            <Typography
                              variant="body1"
                              fontWeight={isCurrentUser ? 'bold' : 'medium'}
                              sx={{
                                fontSize: '1rem',
                                lineHeight: 1.2,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {displayName}
                            </Typography>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                fontSize: '0.875rem',
                                textTransform: 'uppercase',
                                fontWeight: 'medium',
                                letterSpacing: '0.5px'
                              }}
                            >
                              {slot.type === 'organizer' ? 'Organizer' :
                               slot.type === 'driver' ? 'Driver' : 'Passenger'}
                              {isCurrentUser && ' (You)'}
                            </Typography>
                          </Box>
                          {isOrganizer && slot.type === 'passenger' && slot.user && !isCurrentUser && (
                            <IconButton
                              size="small"
                              onClick={() => handleKickUser(slot.user!.id)}
                              disabled={loading}
                              sx={{
                                color: 'error.main',
                                '&:hover': {
                                  bgcolor: 'error.light',
                                  color: 'error.dark'
                                }
                              }}
                            >
                              <PersonRemove fontSize="small" />
                            </IconButton>
                          )}
                        </Box>
                      ) : (
                        <Box display="flex" alignItems="center" gap={1}>
                          <Avatar
                            sx={{
                              width: 32,
                              height: 32,
                              bgcolor: 'grey.500',
                              color: 'white',
                              fontSize: '0.75rem'
                            }}
                          >
                            ➕
                          </Avatar>
                          <Box flex={1}>
                            <Typography
                              variant="body1"
                              color="text.secondary"
                              sx={{
                                fontSize: '1rem',
                                lineHeight: 1.2,
                                fontWeight: 'bold',
                                color: 'grey.700'
                              }}
                            >
                              {displayName}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{
                                fontSize: '0.875rem',
                                textTransform: 'uppercase',
                                fontWeight: 'bold',
                                letterSpacing: '0.5px',
                                color: 'grey.600'
                              }}
                            >
                              Available
                            </Typography>
                          </Box>
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Grid>
              );
            })}
          </Grid>

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </CardContent>

        <CardActions sx={{ p: 2, pt: 0 }}>
          {!userSignedUp && lobby.status === 'open' && getAvailablePositions().length > 0 && (
            <Box display="flex" gap={1} flexWrap="wrap" width="100%">
              {getAvailablePositions().map(position => (
                <Button
                  key={position}
                  size="medium"
                  variant="contained"
                  color="success"
                  onClick={() => handleSignUp(position)}
                  disabled={loading}
                  sx={{
                    flex: 1,
                    minWidth: 'fit-content',
                    fontWeight: 'bold'
                  }}
                >
                  Join Slot {position}
                </Button>
              ))}
            </Box>
          )}

          {userSignedUp && (
            <Button
              size="medium"
              variant="outlined"
              color="warning"
              startIcon={<ExitToApp />}
              onClick={handleRemoveSignup}
              disabled={loading}
              fullWidth
              sx={{ fontWeight: 'bold' }}
            >
              Leave Lobby (Slot {userSlotPosition})
            </Button>
          )}
        </CardActions>
      </Card>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {isOrganizer && [
          <MenuItem key="edit" onClick={handleMenuClose}>
            <Edit fontSize="small" sx={{ mr: 1 }} />
            Edit Lobby
          </MenuItem>,
          <MenuItem 
            key="delete" 
            onClick={() => {
              handleMenuClose();
              setDeleteDialogOpen(true);
            }}
          >
            <Delete fontSize="small" sx={{ mr: 1 }} />
            Delete Lobby
          </MenuItem>
        ]}
        {userSignedUp && !isOrganizer && (
          <MenuItem onClick={() => {
            handleMenuClose();
            handleRemoveSignup();
          }}>
            <ExitToApp fontSize="small" sx={{ mr: 1 }} />
            Leave Lobby
          </MenuItem>
        )}
      </Menu>

      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Lobby</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{lobby.title}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleDeleteLobby} 
            color="error" 
            variant="contained"
            disabled={loading}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
