import type { NextApiRequest, NextApiResponse } from 'next/types';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';
import { LobbyService } from '../../src/services/lobbyService';

const lobbyService = new LobbyService();

// For now, we'll use a simple admin check. In production, you might want to use environment variables
// or a more sophisticated admin system
const ADMIN_DISCORD_IDS = process.env.ADMIN_DISCORD_IDS?.split(',').map(id => id.trim()).filter(id => id.length > 0) || [];

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session || !session.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Try multiple ways to get the Discord ID
    const discordUserId = (session.user as any).discordId ||
                         (session.user as any).id ||
                         'unknown';

    // Debug logging
    console.log('Session user:', session.user);
    console.log('Extracted Discord ID:', discordUserId);
    console.log('Admin Discord IDs:', ADMIN_DISCORD_IDS);
    console.log('Is admin check:', ADMIN_DISCORD_IDS.includes(discordUserId));

    if (req.method === 'GET') {
      // Get organizer configuration
      try {
        const config = await lobbyService.getOrganizerConfig();
        
        return res.status(200).json({
          success: true,
          config: config || { discordIds: [], lastUpdated: new Date() }
        });
      } catch (error) {
        console.error('Error getting organizer config:', error);
        return res.status(500).json({ 
          success: false, 
          error: 'Failed to get organizer configuration' 
        });
      }
    }

    if (req.method === 'POST') {
      // Update organizer configuration (admin only)
      
      // Check if user is admin
      if (!ADMIN_DISCORD_IDS.includes(discordUserId)) {
        return res.status(403).json({ 
          success: false, 
          error: 'Admin access required to modify organizer configuration' 
        });
      }

      const { discordIds } = req.body;

      if (!Array.isArray(discordIds)) {
        return res.status(400).json({ 
          success: false, 
          error: 'discordIds must be an array' 
        });
      }

      // Validate Discord IDs (basic validation)
      const validIds = discordIds.filter(id => 
        typeof id === 'string' && id.length > 0 && /^\d+$/.test(id)
      );

      if (validIds.length !== discordIds.length) {
        return res.status(400).json({ 
          success: false, 
          error: 'All Discord IDs must be valid numeric strings' 
        });
      }

      try {
        const config = await lobbyService.updateOrganizerConfig(validIds);
        
        return res.status(200).json({
          success: true,
          config,
          message: 'Organizer configuration updated successfully'
        });
      } catch (error) {
        console.error('Error updating organizer config:', error);
        return res.status(500).json({ 
          success: false, 
          error: 'Failed to update organizer configuration' 
        });
      }
    }

    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });

  } catch (error) {
    console.error('Error in organizers API:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
}
